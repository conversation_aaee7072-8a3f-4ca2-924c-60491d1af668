#include "stm32f10x.h"
#include "sys.h"
#include "delay.h"
#include "Usart1.h"
#include "oled.h"
#include "PCA9685.h"
#include "TB6612.h"
#include "Usart5.h"
#include "pid_control.h"  
#include "Usart2.h"


void init(){
    delay_init(8);
    OLED_Init();
    PCA9685_Init();
    MOTOR_Init();
    Usart2_Init(115200);
    Usart5_Init(9600);
    Encoder_Init();
    PID_Control_Init();  
}


int main(){

    init();

    // Position_Control_Start(Motor1, 1, 100);
    // Position_Control_Start(Motor2, 1, 100);
    // Position_Control_Start(Motor3, 1, 100);
    // Position_Control_Start(Motor4, 1, 100);

    Speed_Control_Start(Motor1, 40.0);
    Speed_Control_Start(Motor2, 40.0);
    Speed_Control_Start(Motor3, 40.0);
    Speed_Control_Start(Motor4, 40.0);

    while(1){
    }
}