Component: ARM Compiler 5.06 update 7 (build 960) Tool: armlink [4d3601]

==============================================================================

Section Cross References

    system_stm32f10x.o(i.SetSysClock) refers to system_stm32f10x.o(i.SetSysClockTo72) for SetSysClockTo72
    system_stm32f10x.o(i.SystemCoreClockUpdate) refers to system_stm32f10x.o(.data) for SystemCoreClock
    system_stm32f10x.o(i.SystemInit) refers to system_stm32f10x.o(i.SetSysClock) for SetSysClock
    startup_stm32f10x_hd.o(STACK) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32f10x_hd.o(HEAP) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32f10x_hd.o(RESET) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32f10x_hd.o(RESET) refers to startup_stm32f10x_hd.o(STACK) for __initial_sp
    startup_stm32f10x_hd.o(RESET) refers to startup_stm32f10x_hd.o(.text) for Reset_Handler
    startup_stm32f10x_hd.o(RESET) refers to pid_control.o(i.TIM6_IRQHandler) for TIM6_IRQHandler
    startup_stm32f10x_hd.o(.text) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32f10x_hd.o(.text) refers to system_stm32f10x.o(i.SystemInit) for SystemInit
    startup_stm32f10x_hd.o(.text) refers to __main.o(!!!main) for __main
    startup_stm32f10x_hd.o(.text) refers to startup_stm32f10x_hd.o(HEAP) for Heap_Mem
    startup_stm32f10x_hd.o(.text) refers to startup_stm32f10x_hd.o(STACK) for Stack_Mem
    stm32f10x_adc.o(i.ADC_DeInit) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphResetCmd) for RCC_APB2PeriphResetCmd
    stm32f10x_bkp.o(i.BKP_DeInit) refers to stm32f10x_rcc.o(i.RCC_BackupResetCmd) for RCC_BackupResetCmd
    stm32f10x_can.o(i.CAN_DeInit) refers to stm32f10x_rcc.o(i.RCC_APB1PeriphResetCmd) for RCC_APB1PeriphResetCmd
    stm32f10x_can.o(i.CAN_GetITStatus) refers to stm32f10x_can.o(i.CheckITStatus) for CheckITStatus
    stm32f10x_cec.o(i.CEC_DeInit) refers to stm32f10x_rcc.o(i.RCC_APB1PeriphResetCmd) for RCC_APB1PeriphResetCmd
    stm32f10x_dac.o(i.DAC_DeInit) refers to stm32f10x_rcc.o(i.RCC_APB1PeriphResetCmd) for RCC_APB1PeriphResetCmd
    stm32f10x_flash.o(i.FLASH_EnableWriteProtection) refers to stm32f10x_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f10x_flash.o(i.FLASH_EraseAllBank1Pages) refers to stm32f10x_flash.o(i.FLASH_WaitForLastBank1Operation) for FLASH_WaitForLastBank1Operation
    stm32f10x_flash.o(i.FLASH_EraseAllPages) refers to stm32f10x_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f10x_flash.o(i.FLASH_EraseOptionBytes) refers to stm32f10x_flash.o(i.FLASH_GetReadOutProtectionStatus) for FLASH_GetReadOutProtectionStatus
    stm32f10x_flash.o(i.FLASH_EraseOptionBytes) refers to stm32f10x_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f10x_flash.o(i.FLASH_ErasePage) refers to stm32f10x_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f10x_flash.o(i.FLASH_ProgramHalfWord) refers to stm32f10x_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f10x_flash.o(i.FLASH_ProgramOptionByteData) refers to stm32f10x_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f10x_flash.o(i.FLASH_ProgramWord) refers to stm32f10x_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f10x_flash.o(i.FLASH_ReadOutProtection) refers to stm32f10x_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f10x_flash.o(i.FLASH_UserOptionByteConfig) refers to stm32f10x_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f10x_flash.o(i.FLASH_WaitForLastBank1Operation) refers to stm32f10x_flash.o(i.FLASH_GetBank1Status) for FLASH_GetBank1Status
    stm32f10x_flash.o(i.FLASH_WaitForLastOperation) refers to stm32f10x_flash.o(i.FLASH_GetBank1Status) for FLASH_GetBank1Status
    stm32f10x_gpio.o(i.GPIO_AFIODeInit) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphResetCmd) for RCC_APB2PeriphResetCmd
    stm32f10x_gpio.o(i.GPIO_DeInit) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphResetCmd) for RCC_APB2PeriphResetCmd
    stm32f10x_i2c.o(i.I2C_DeInit) refers to stm32f10x_rcc.o(i.RCC_APB1PeriphResetCmd) for RCC_APB1PeriphResetCmd
    stm32f10x_i2c.o(i.I2C_Init) refers to stm32f10x_rcc.o(i.RCC_GetClocksFreq) for RCC_GetClocksFreq
    stm32f10x_pwr.o(i.PWR_DeInit) refers to stm32f10x_rcc.o(i.RCC_APB1PeriphResetCmd) for RCC_APB1PeriphResetCmd
    stm32f10x_rcc.o(i.RCC_GetClocksFreq) refers to stm32f10x_rcc.o(.data) for APBAHBPrescTable
    stm32f10x_rcc.o(i.RCC_WaitForHSEStartUp) refers to stm32f10x_rcc.o(i.RCC_GetFlagStatus) for RCC_GetFlagStatus
    stm32f10x_rtc.o(i.RTC_SetAlarm) refers to stm32f10x_rtc.o(i.RTC_EnterConfigMode) for RTC_EnterConfigMode
    stm32f10x_rtc.o(i.RTC_SetAlarm) refers to stm32f10x_rtc.o(i.RTC_ExitConfigMode) for RTC_ExitConfigMode
    stm32f10x_rtc.o(i.RTC_SetCounter) refers to stm32f10x_rtc.o(i.RTC_EnterConfigMode) for RTC_EnterConfigMode
    stm32f10x_rtc.o(i.RTC_SetCounter) refers to stm32f10x_rtc.o(i.RTC_ExitConfigMode) for RTC_ExitConfigMode
    stm32f10x_rtc.o(i.RTC_SetPrescaler) refers to stm32f10x_rtc.o(i.RTC_EnterConfigMode) for RTC_EnterConfigMode
    stm32f10x_rtc.o(i.RTC_SetPrescaler) refers to stm32f10x_rtc.o(i.RTC_ExitConfigMode) for RTC_ExitConfigMode
    stm32f10x_spi.o(i.I2S_Init) refers to stm32f10x_rcc.o(i.RCC_GetClocksFreq) for RCC_GetClocksFreq
    stm32f10x_spi.o(i.SPI_I2S_DeInit) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphResetCmd) for RCC_APB2PeriphResetCmd
    stm32f10x_spi.o(i.SPI_I2S_DeInit) refers to stm32f10x_rcc.o(i.RCC_APB1PeriphResetCmd) for RCC_APB1PeriphResetCmd
    stm32f10x_tim.o(i.TIM_DeInit) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphResetCmd) for RCC_APB2PeriphResetCmd
    stm32f10x_tim.o(i.TIM_DeInit) refers to stm32f10x_rcc.o(i.RCC_APB1PeriphResetCmd) for RCC_APB1PeriphResetCmd
    stm32f10x_tim.o(i.TIM_ETRClockMode1Config) refers to stm32f10x_tim.o(i.TIM_ETRConfig) for TIM_ETRConfig
    stm32f10x_tim.o(i.TIM_ETRClockMode2Config) refers to stm32f10x_tim.o(i.TIM_ETRConfig) for TIM_ETRConfig
    stm32f10x_tim.o(i.TIM_ICInit) refers to stm32f10x_tim.o(i.TI1_Config) for TI1_Config
    stm32f10x_tim.o(i.TIM_ICInit) refers to stm32f10x_tim.o(i.TIM_SetIC1Prescaler) for TIM_SetIC1Prescaler
    stm32f10x_tim.o(i.TIM_ICInit) refers to stm32f10x_tim.o(i.TI2_Config) for TI2_Config
    stm32f10x_tim.o(i.TIM_ICInit) refers to stm32f10x_tim.o(i.TIM_SetIC2Prescaler) for TIM_SetIC2Prescaler
    stm32f10x_tim.o(i.TIM_ICInit) refers to stm32f10x_tim.o(i.TI3_Config) for TI3_Config
    stm32f10x_tim.o(i.TIM_ICInit) refers to stm32f10x_tim.o(i.TIM_SetIC3Prescaler) for TIM_SetIC3Prescaler
    stm32f10x_tim.o(i.TIM_ICInit) refers to stm32f10x_tim.o(i.TI4_Config) for TI4_Config
    stm32f10x_tim.o(i.TIM_ICInit) refers to stm32f10x_tim.o(i.TIM_SetIC4Prescaler) for TIM_SetIC4Prescaler
    stm32f10x_tim.o(i.TIM_ITRxExternalClockConfig) refers to stm32f10x_tim.o(i.TIM_SelectInputTrigger) for TIM_SelectInputTrigger
    stm32f10x_tim.o(i.TIM_PWMIConfig) refers to stm32f10x_tim.o(i.TI1_Config) for TI1_Config
    stm32f10x_tim.o(i.TIM_PWMIConfig) refers to stm32f10x_tim.o(i.TIM_SetIC1Prescaler) for TIM_SetIC1Prescaler
    stm32f10x_tim.o(i.TIM_PWMIConfig) refers to stm32f10x_tim.o(i.TI2_Config) for TI2_Config
    stm32f10x_tim.o(i.TIM_PWMIConfig) refers to stm32f10x_tim.o(i.TIM_SetIC2Prescaler) for TIM_SetIC2Prescaler
    stm32f10x_tim.o(i.TIM_TIxExternalClockConfig) refers to stm32f10x_tim.o(i.TI2_Config) for TI2_Config
    stm32f10x_tim.o(i.TIM_TIxExternalClockConfig) refers to stm32f10x_tim.o(i.TI1_Config) for TI1_Config
    stm32f10x_tim.o(i.TIM_TIxExternalClockConfig) refers to stm32f10x_tim.o(i.TIM_SelectInputTrigger) for TIM_SelectInputTrigger
    stm32f10x_usart.o(i.USART_DeInit) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphResetCmd) for RCC_APB2PeriphResetCmd
    stm32f10x_usart.o(i.USART_DeInit) refers to stm32f10x_rcc.o(i.RCC_APB1PeriphResetCmd) for RCC_APB1PeriphResetCmd
    stm32f10x_usart.o(i.USART_Init) refers to stm32f10x_rcc.o(i.RCC_GetClocksFreq) for RCC_GetClocksFreq
    stm32f10x_wwdg.o(i.WWDG_DeInit) refers to stm32f10x_rcc.o(i.RCC_APB1PeriphResetCmd) for RCC_APB1PeriphResetCmd
    led.o(i.LED_Init) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphClockCmd) for RCC_APB2PeriphClockCmd
    led.o(i.LED_Init) refers to stm32f10x_gpio.o(i.GPIO_Init) for GPIO_Init
    led.o(i.LED_Init) refers to stm32f10x_gpio.o(i.GPIO_SetBits) for GPIO_SetBits
    led.o(i.LED_OFF) refers to stm32f10x_gpio.o(i.GPIO_SetBits) for GPIO_SetBits
    led.o(i.LED_ON) refers to stm32f10x_gpio.o(i.GPIO_ResetBits) for GPIO_ResetBits
    tb6612.o(i.Hardware_PWM_Init) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphClockCmd) for RCC_APB2PeriphClockCmd
    tb6612.o(i.Hardware_PWM_Init) refers to stm32f10x_rcc.o(i.RCC_APB1PeriphClockCmd) for RCC_APB1PeriphClockCmd
    tb6612.o(i.Hardware_PWM_Init) refers to stm32f10x_gpio.o(i.GPIO_Init) for GPIO_Init
    tb6612.o(i.Hardware_PWM_Init) refers to stm32f10x_tim.o(i.TIM_TimeBaseInit) for TIM_TimeBaseInit
    tb6612.o(i.Hardware_PWM_Init) refers to stm32f10x_tim.o(i.TIM_OC1Init) for TIM_OC1Init
    tb6612.o(i.Hardware_PWM_Init) refers to stm32f10x_tim.o(i.TIM_OC1PreloadConfig) for TIM_OC1PreloadConfig
    tb6612.o(i.Hardware_PWM_Init) refers to stm32f10x_tim.o(i.TIM_OC2Init) for TIM_OC2Init
    tb6612.o(i.Hardware_PWM_Init) refers to stm32f10x_tim.o(i.TIM_OC2PreloadConfig) for TIM_OC2PreloadConfig
    tb6612.o(i.Hardware_PWM_Init) refers to stm32f10x_tim.o(i.TIM_OC3Init) for TIM_OC3Init
    tb6612.o(i.Hardware_PWM_Init) refers to stm32f10x_tim.o(i.TIM_OC3PreloadConfig) for TIM_OC3PreloadConfig
    tb6612.o(i.Hardware_PWM_Init) refers to stm32f10x_tim.o(i.TIM_OC4Init) for TIM_OC4Init
    tb6612.o(i.Hardware_PWM_Init) refers to stm32f10x_tim.o(i.TIM_OC4PreloadConfig) for TIM_OC4PreloadConfig
    tb6612.o(i.Hardware_PWM_Init) refers to stm32f10x_tim.o(i.TIM_Cmd) for TIM_Cmd
    tb6612.o(i.Hardware_PWM_Init) refers to stm32f10x_tim.o(i.TIM_CtrlPWMOutputs) for TIM_CtrlPWMOutputs
    tb6612.o(i.MOTOR_Init) refers to tb6612.o(i.Hardware_PWM_Init) for Hardware_PWM_Init
    tb6612.o(i.MOTOR_Init) refers to tb6612.o(i.Motor_Direction_Init) for Motor_Direction_Init
    tb6612.o(i.MOTOR_Init) refers to tb6612.o(i.Move_stop) for Move_stop
    tb6612.o(i.Motor_Direction_Init) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphClockCmd) for RCC_APB2PeriphClockCmd
    tb6612.o(i.Motor_Direction_Init) refers to stm32f10x_gpio.o(i.GPIO_Init) for GPIO_Init
    tb6612.o(i.Motor_SetDirection) refers to stm32f10x_gpio.o(i.GPIO_SetBits) for GPIO_SetBits
    tb6612.o(i.Motor_SetDirection) refers to stm32f10x_gpio.o(i.GPIO_ResetBits) for GPIO_ResetBits
    tb6612.o(i.Motor_SetPWM) refers to stm32f10x_tim.o(i.TIM_SetCompare2) for TIM_SetCompare2
    tb6612.o(i.Motor_SetPWM) refers to stm32f10x_tim.o(i.TIM_SetCompare1) for TIM_SetCompare1
    tb6612.o(i.Motor_SetPWM) refers to stm32f10x_tim.o(i.TIM_SetCompare4) for TIM_SetCompare4
    tb6612.o(i.Motor_SetPWM) refers to stm32f10x_tim.o(i.TIM_SetCompare3) for TIM_SetCompare3
    tb6612.o(i.Move_stop) refers to tb6612.o(i.Motor_SetDirection) for Motor_SetDirection
    tb6612.o(i.Move_stop) refers to tb6612.o(i.Motor_SetPWM) for Motor_SetPWM
    tb6612.o(i.Set_Motor_PWM) refers to tb6612.o(i.Motor_SetDirection) for Motor_SetDirection
    tb6612.o(i.Set_Motor_PWM) refers to tb6612.o(i.Motor_SetPWM) for Motor_SetPWM
    oled.o(i.OLED_Clear) refers to oled.o(i.OLED_SetCursor) for OLED_SetCursor
    oled.o(i.OLED_Clear) refers to oled.o(i.OLED_WriteData) for OLED_WriteData
    oled.o(i.OLED_I2C_Init) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphClockCmd) for RCC_APB2PeriphClockCmd
    oled.o(i.OLED_I2C_Init) refers to stm32f10x_gpio.o(i.GPIO_Init) for GPIO_Init
    oled.o(i.OLED_I2C_Init) refers to stm32f10x_gpio.o(i.GPIO_WriteBit) for GPIO_WriteBit
    oled.o(i.OLED_I2C_SendByte) refers to stm32f10x_gpio.o(i.GPIO_WriteBit) for GPIO_WriteBit
    oled.o(i.OLED_I2C_Start) refers to stm32f10x_gpio.o(i.GPIO_WriteBit) for GPIO_WriteBit
    oled.o(i.OLED_I2C_Stop) refers to stm32f10x_gpio.o(i.GPIO_WriteBit) for GPIO_WriteBit
    oled.o(i.OLED_Init) refers to oled.o(i.OLED_I2C_Init) for OLED_I2C_Init
    oled.o(i.OLED_Init) refers to oled.o(i.OLED_WriteCommand) for OLED_WriteCommand
    oled.o(i.OLED_Init) refers to oled.o(i.OLED_Clear) for OLED_Clear
    oled.o(i.OLED_SetCursor) refers to oled.o(i.OLED_WriteCommand) for OLED_WriteCommand
    oled.o(i.OLED_ShowBinNum) refers to oled.o(i.OLED_Pow) for OLED_Pow
    oled.o(i.OLED_ShowBinNum) refers to oled.o(i.OLED_ShowChar) for OLED_ShowChar
    oled.o(i.OLED_ShowChar) refers to oled.o(i.OLED_SetCursor) for OLED_SetCursor
    oled.o(i.OLED_ShowChar) refers to oled.o(i.OLED_WriteData) for OLED_WriteData
    oled.o(i.OLED_ShowChar) refers to oled.o(.constdata) for OLED_F8x16
    oled.o(i.OLED_ShowHexNum) refers to oled.o(i.OLED_Pow) for OLED_Pow
    oled.o(i.OLED_ShowHexNum) refers to oled.o(i.OLED_ShowChar) for OLED_ShowChar
    oled.o(i.OLED_ShowNum) refers to oled.o(i.OLED_Pow) for OLED_Pow
    oled.o(i.OLED_ShowNum) refers to oled.o(i.OLED_ShowChar) for OLED_ShowChar
    oled.o(i.OLED_ShowSignedNum) refers to oled.o(i.OLED_ShowChar) for OLED_ShowChar
    oled.o(i.OLED_ShowSignedNum) refers to oled.o(i.OLED_Pow) for OLED_Pow
    oled.o(i.OLED_ShowString) refers to oled.o(i.OLED_ShowChar) for OLED_ShowChar
    oled.o(i.OLED_WriteCommand) refers to oled.o(i.OLED_I2C_Start) for OLED_I2C_Start
    oled.o(i.OLED_WriteCommand) refers to oled.o(i.OLED_I2C_SendByte) for OLED_I2C_SendByte
    oled.o(i.OLED_WriteCommand) refers to oled.o(i.OLED_I2C_Stop) for OLED_I2C_Stop
    oled.o(i.OLED_WriteData) refers to oled.o(i.OLED_I2C_Start) for OLED_I2C_Start
    oled.o(i.OLED_WriteData) refers to oled.o(i.OLED_I2C_SendByte) for OLED_I2C_SendByte
    oled.o(i.OLED_WriteData) refers to oled.o(i.OLED_I2C_Stop) for OLED_I2C_Stop
    pca9685.o(i.PCA9685_Init) refers to iic.o(i.IIC_Init) for IIC_Init
    pca9685.o(i.PCA9685_Init) refers to pca9685.o(i.PCA9685_write) for PCA9685_write
    pca9685.o(i.PCA9685_Init) refers to delay.o(i.delay_ms) for delay_ms
    pca9685.o(i.PCA9685_Init) refers to pca9685.o(i.setPWMFreq) for setPWMFreq
    pca9685.o(i.PCA9685_read) refers to iic.o(i.IIC_Start) for IIC_Start
    pca9685.o(i.PCA9685_read) refers to iic.o(i.IIC_Send_Byte) for IIC_Send_Byte
    pca9685.o(i.PCA9685_read) refers to iic.o(i.IIC_Wait_Ack) for IIC_Wait_Ack
    pca9685.o(i.PCA9685_read) refers to iic.o(i.IIC_Read_Byte) for IIC_Read_Byte
    pca9685.o(i.PCA9685_read) refers to iic.o(i.IIC_Stop) for IIC_Stop
    pca9685.o(i.PCA9685_write) refers to iic.o(i.IIC_Start) for IIC_Start
    pca9685.o(i.PCA9685_write) refers to iic.o(i.IIC_Send_Byte) for IIC_Send_Byte
    pca9685.o(i.PCA9685_write) refers to iic.o(i.IIC_Wait_Ack) for IIC_Wait_Ack
    pca9685.o(i.PCA9685_write) refers to iic.o(i.IIC_Stop) for IIC_Stop
    pca9685.o(i.calculate_PWM) refers to dflt_clz.o(x$fpl$dfltu) for __aeabi_ui2d
    pca9685.o(i.calculate_PWM) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    pca9685.o(i.calculate_PWM) refers to ddiv.o(x$fpl$ddiv) for __aeabi_ddiv
    pca9685.o(i.calculate_PWM) refers to daddsub_clz.o(x$fpl$dadd) for __aeabi_dadd
    pca9685.o(i.calculate_PWM) refers to dfixu.o(x$fpl$dfixu) for __aeabi_d2uiz
    pca9685.o(i.crazyMe) refers to pca9685.o(i.calculate_PWM) for calculate_PWM
    pca9685.o(i.crazyMe) refers to pca9685.o(i.setPWM) for setPWM
    pca9685.o(i.crazyMe) refers to delay.o(i.delay_ms) for delay_ms
    pca9685.o(i.setPWM) refers to pca9685.o(i.PCA9685_write) for PCA9685_write
    pca9685.o(i.setPWMFreq) refers to dflt_clz.o(x$fpl$dflt) for __aeabi_i2d
    pca9685.o(i.setPWMFreq) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    pca9685.o(i.setPWMFreq) refers to ddiv.o(x$fpl$ddiv) for __aeabi_ddiv
    pca9685.o(i.setPWMFreq) refers to daddsub_clz.o(x$fpl$dadd) for __aeabi_dadd
    pca9685.o(i.setPWMFreq) refers to floor.o(i.floor) for floor
    pca9685.o(i.setPWMFreq) refers to dfixu.o(x$fpl$dfixu) for __aeabi_d2uiz
    pca9685.o(i.setPWMFreq) refers to pca9685.o(i.PCA9685_read) for PCA9685_read
    pca9685.o(i.setPWMFreq) refers to pca9685.o(i.PCA9685_write) for PCA9685_write
    pca9685.o(i.setPWMFreq) refers to delay.o(i.delay_ms) for delay_ms
    encoder.o(i.Encoder1_Init) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphClockCmd) for RCC_APB2PeriphClockCmd
    encoder.o(i.Encoder1_Init) refers to stm32f10x_gpio.o(i.GPIO_PinRemapConfig) for GPIO_PinRemapConfig
    encoder.o(i.Encoder1_Init) refers to stm32f10x_gpio.o(i.GPIO_Init) for GPIO_Init
    encoder.o(i.Encoder1_Init) refers to stm32f10x_tim.o(i.TIM_TimeBaseInit) for TIM_TimeBaseInit
    encoder.o(i.Encoder1_Init) refers to stm32f10x_tim.o(i.TIM_EncoderInterfaceConfig) for TIM_EncoderInterfaceConfig
    encoder.o(i.Encoder1_Init) refers to stm32f10x_tim.o(i.TIM_SetCounter) for TIM_SetCounter
    encoder.o(i.Encoder1_Init) refers to stm32f10x_tim.o(i.TIM_CtrlPWMOutputs) for TIM_CtrlPWMOutputs
    encoder.o(i.Encoder1_Init) refers to stm32f10x_tim.o(i.TIM_Cmd) for TIM_Cmd
    encoder.o(i.Encoder2_Init) refers to stm32f10x_rcc.o(i.RCC_APB1PeriphClockCmd) for RCC_APB1PeriphClockCmd
    encoder.o(i.Encoder2_Init) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphClockCmd) for RCC_APB2PeriphClockCmd
    encoder.o(i.Encoder2_Init) refers to stm32f10x_gpio.o(i.GPIO_PinRemapConfig) for GPIO_PinRemapConfig
    encoder.o(i.Encoder2_Init) refers to stm32f10x_gpio.o(i.GPIO_Init) for GPIO_Init
    encoder.o(i.Encoder2_Init) refers to stm32f10x_tim.o(i.TIM_TimeBaseInit) for TIM_TimeBaseInit
    encoder.o(i.Encoder2_Init) refers to stm32f10x_tim.o(i.TIM_EncoderInterfaceConfig) for TIM_EncoderInterfaceConfig
    encoder.o(i.Encoder2_Init) refers to stm32f10x_tim.o(i.TIM_SetCounter) for TIM_SetCounter
    encoder.o(i.Encoder2_Init) refers to stm32f10x_tim.o(i.TIM_Cmd) for TIM_Cmd
    encoder.o(i.Encoder3_Init) refers to stm32f10x_rcc.o(i.RCC_APB1PeriphClockCmd) for RCC_APB1PeriphClockCmd
    encoder.o(i.Encoder3_Init) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphClockCmd) for RCC_APB2PeriphClockCmd
    encoder.o(i.Encoder3_Init) refers to stm32f10x_gpio.o(i.GPIO_PinRemapConfig) for GPIO_PinRemapConfig
    encoder.o(i.Encoder3_Init) refers to stm32f10x_gpio.o(i.GPIO_Init) for GPIO_Init
    encoder.o(i.Encoder3_Init) refers to stm32f10x_tim.o(i.TIM_TimeBaseInit) for TIM_TimeBaseInit
    encoder.o(i.Encoder3_Init) refers to stm32f10x_tim.o(i.TIM_EncoderInterfaceConfig) for TIM_EncoderInterfaceConfig
    encoder.o(i.Encoder3_Init) refers to stm32f10x_tim.o(i.TIM_SetCounter) for TIM_SetCounter
    encoder.o(i.Encoder3_Init) refers to stm32f10x_tim.o(i.TIM_Cmd) for TIM_Cmd
    encoder.o(i.Encoder4_Init) refers to stm32f10x_rcc.o(i.RCC_APB1PeriphClockCmd) for RCC_APB1PeriphClockCmd
    encoder.o(i.Encoder4_Init) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphClockCmd) for RCC_APB2PeriphClockCmd
    encoder.o(i.Encoder4_Init) refers to stm32f10x_gpio.o(i.GPIO_PinRemapConfig) for GPIO_PinRemapConfig
    encoder.o(i.Encoder4_Init) refers to stm32f10x_gpio.o(i.GPIO_Init) for GPIO_Init
    encoder.o(i.Encoder4_Init) refers to stm32f10x_tim.o(i.TIM_TimeBaseInit) for TIM_TimeBaseInit
    encoder.o(i.Encoder4_Init) refers to stm32f10x_tim.o(i.TIM_EncoderInterfaceConfig) for TIM_EncoderInterfaceConfig
    encoder.o(i.Encoder4_Init) refers to stm32f10x_tim.o(i.TIM_SetCounter) for TIM_SetCounter
    encoder.o(i.Encoder4_Init) refers to stm32f10x_tim.o(i.TIM_Cmd) for TIM_Cmd
    encoder.o(i.Encoder_Init) refers to encoder.o(i.Encoder1_Init) for Encoder1_Init
    encoder.o(i.Encoder_Init) refers to encoder.o(i.Encoder2_Init) for Encoder2_Init
    encoder.o(i.Encoder_Init) refers to encoder.o(i.Encoder3_Init) for Encoder3_Init
    encoder.o(i.Encoder_Init) refers to encoder.o(i.Encoder4_Init) for Encoder4_Init
    encoder.o(i.Get_Encoder_Count) refers to stm32f10x_tim.o(i.TIM_GetCounter) for TIM_GetCounter
    encoder.o(i.Reset_All_Encoders) refers to stm32f10x_tim.o(i.TIM_SetCounter) for TIM_SetCounter
    encoder.o(i.Reset_Encoder) refers to stm32f10x_tim.o(i.TIM_SetCounter) for TIM_SetCounter
    pid_control.o(i.Get_Accumulated_Position) refers to encoder.o(i.Get_Encoder_Count) for Get_Encoder_Count
    pid_control.o(i.Get_Accumulated_Position) refers to fflt_clz.o(x$fpl$fflt) for __aeabi_i2f
    pid_control.o(i.Get_Accumulated_Position) refers to fmul.o(x$fpl$fmul) for __aeabi_fmul
    pid_control.o(i.Get_Accumulated_Position) refers to fdiv.o(x$fpl$fdiv) for __aeabi_fdiv
    pid_control.o(i.Get_Accumulated_Position) refers to encoder.o(i.Reset_Encoder) for Reset_Encoder
    pid_control.o(i.Get_Accumulated_Position) refers to pid_control.o(.bss) for motor_states
    pid_control.o(i.Get_Motor_Speed) refers to pid_control.o(.bss) for motor_states
    pid_control.o(i.Limit_Value) refers to frleqf.o(x$fpl$frleqf) for __aeabi_cfrcmple
    pid_control.o(i.Motor_Control_Stop) refers to tb6612.o(i.Set_Motor_PWM) for Set_Motor_PWM
    pid_control.o(i.Motor_Control_Stop) refers to pid_control.o(.bss) for motor_states
    pid_control.o(i.Motor_Control_Stop_All) refers to pid_control.o(i.Motor_Control_Stop) for Motor_Control_Stop
    pid_control.o(i.PID_Control_Init) refers to stm32f10x_rcc.o(i.RCC_APB1PeriphClockCmd) for RCC_APB1PeriphClockCmd
    pid_control.o(i.PID_Control_Init) refers to stm32f10x_tim.o(i.TIM_TimeBaseInit) for TIM_TimeBaseInit
    pid_control.o(i.PID_Control_Init) refers to stm32f10x_tim.o(i.TIM_ITConfig) for TIM_ITConfig
    pid_control.o(i.PID_Control_Init) refers to misc.o(i.NVIC_Init) for NVIC_Init
    pid_control.o(i.PID_Control_Init) refers to pid_control.o(i.Reset_Motor_State) for Reset_Motor_State
    pid_control.o(i.PID_Control_Init) refers to tb6612.o(i.Move_stop) for Move_stop
    pid_control.o(i.PID_Control_Init) refers to stm32f10x_tim.o(i.TIM_Cmd) for TIM_Cmd
    pid_control.o(i.Position_Control_Start) refers to f2d.o(x$fpl$f2d) for __aeabi_f2d
    pid_control.o(i.Position_Control_Start) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    pid_control.o(i.Position_Control_Start) refers to d2f.o(x$fpl$d2f) for __aeabi_d2f
    pid_control.o(i.Position_Control_Start) refers to pid_control.o(i.Reset_Accumulated_Position) for Reset_Accumulated_Position
    pid_control.o(i.Position_Control_Start) refers to pid_control.o(.bss) for motor_states
    pid_control.o(i.Position_PID_RPM) refers to fflt_clz.o(x$fpl$fflt) for __aeabi_i2f
    pid_control.o(i.Position_PID_RPM) refers to faddsub_clz.o(x$fpl$fadd) for __aeabi_fadd
    pid_control.o(i.Position_PID_RPM) refers to pid_control.o(i.Limit_Value) for Limit_Value
    pid_control.o(i.Position_PID_RPM) refers to faddsub_clz.o(x$fpl$fsub) for __aeabi_fsub
    pid_control.o(i.Position_PID_RPM) refers to fmul.o(x$fpl$fmul) for __aeabi_fmul
    pid_control.o(i.Position_PID_RPM) refers to fdiv.o(x$fpl$fdiv) for __aeabi_fdiv
    pid_control.o(i.Position_PID_RPM) refers to pid_control.o(.bss) for bias
    pid_control.o(i.Position_PID_RPM) refers to pid_control.o(.data) for Position_KD
    pid_control.o(i.Reset_Accumulated_Position) refers to encoder.o(i.Reset_Encoder) for Reset_Encoder
    pid_control.o(i.Reset_Accumulated_Position) refers to pid_control.o(.bss) for motor_states
    pid_control.o(i.Reset_Motor_State) refers to encoder.o(i.Reset_Encoder) for Reset_Encoder
    pid_control.o(i.Reset_Motor_State) refers to pid_control.o(.bss) for motor_states
    pid_control.o(i.Speed_Control_Start) refers to encoder.o(i.Reset_Encoder) for Reset_Encoder
    pid_control.o(i.Speed_Control_Start) refers to pid_control.o(i.Speed_PID_Calculate) for Speed_PID_Calculate
    pid_control.o(i.Speed_Control_Start) refers to pid_control.o(.bss) for motor_states
    pid_control.o(i.Speed_PID_Calculate) refers to feqf.o(x$fpl$feqf) for __aeabi_cfcmpeq
    pid_control.o(i.Speed_PID_Calculate) refers to fflt_clz.o(x$fpl$fflt) for __aeabi_i2f
    pid_control.o(i.Speed_PID_Calculate) refers to fmul.o(x$fpl$fmul) for __aeabi_fmul
    pid_control.o(i.Speed_PID_Calculate) refers to faddsub_clz.o(x$fpl$frsb) for __aeabi_frsub
    pid_control.o(i.Speed_PID_Calculate) refers to faddsub_clz.o(x$fpl$fadd) for __aeabi_fadd
    pid_control.o(i.Speed_PID_Calculate) refers to faddsub_clz.o(x$fpl$fsub) for __aeabi_fsub
    pid_control.o(i.Speed_PID_Calculate) refers to pid_control.o(i.Limit_Value) for Limit_Value
    pid_control.o(i.Speed_PID_Calculate) refers to ffix.o(x$fpl$ffix) for __aeabi_f2iz
    pid_control.o(i.Speed_PID_Calculate) refers to pid_control.o(.bss) for last_error
    pid_control.o(i.TIM6_IRQHandler) refers to stm32f10x_tim.o(i.TIM_GetITStatus) for TIM_GetITStatus
    pid_control.o(i.TIM6_IRQHandler) refers to stm32f10x_tim.o(i.TIM_ClearITPendingBit) for TIM_ClearITPendingBit
    pid_control.o(i.TIM6_IRQHandler) refers to pid_control.o(i.Get_Accumulated_Position) for Get_Accumulated_Position
    pid_control.o(i.TIM6_IRQHandler) refers to pid_control.o(i.Get_Motor_Speed) for Get_Motor_Speed
    pid_control.o(i.TIM6_IRQHandler) refers to ffix.o(x$fpl$ffix) for __aeabi_f2iz
    pid_control.o(i.TIM6_IRQHandler) refers to pid_control.o(i.Position_PID_RPM) for Position_PID_RPM
    pid_control.o(i.TIM6_IRQHandler) refers to pid_control.o(i.Limit_Value) for Limit_Value
    pid_control.o(i.TIM6_IRQHandler) refers to tb6612.o(i.Set_Motor_PWM) for Set_Motor_PWM
    pid_control.o(i.TIM6_IRQHandler) refers to pid_control.o(i.Speed_PID_Calculate) for Speed_PID_Calculate
    pid_control.o(i.TIM6_IRQHandler) refers to pid_control.o(.bss) for motor_states
    pid_control.o(i.TIM6_IRQHandler) refers to pid_control.o(.data) for Cascade_Speed_KD
    sys.o(i.MY_NVIC_Init) refers to sys.o(i.MY_NVIC_PriorityGroupConfig) for MY_NVIC_PriorityGroupConfig
    sys.o(i.Stm32_Clock_Init) refers to sys.o(i.MY_NVIC_PriorityGroupConfig) for MY_NVIC_PriorityGroupConfig
    sys.o(i.Stm32_Clock_Init) refers to sys.o(i.MY_NVIC_SetVectorTable) for MY_NVIC_SetVectorTable
    sys.o(i.Sys_Standby) refers to sys.o(.emb_text) for WFI_SET
    usart1.o(i.Usart1_Init) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphClockCmd) for RCC_APB2PeriphClockCmd
    usart1.o(i.Usart1_Init) refers to stm32f10x_gpio.o(i.GPIO_Init) for GPIO_Init
    usart1.o(i.Usart1_Init) refers to stm32f10x_usart.o(i.USART_Init) for USART_Init
    usart1.o(i.Usart1_Init) refers to stm32f10x_usart.o(i.USART_Cmd) for USART_Cmd
    usart1.o(i.Usart1_ReceiveChar) refers to stm32f10x_usart.o(i.USART_GetFlagStatus) for USART_GetFlagStatus
    usart1.o(i.Usart1_ReceiveChar) refers to stm32f10x_usart.o(i.USART_ReceiveData) for USART_ReceiveData
    usart1.o(i.Usart1_SendChar) refers to stm32f10x_usart.o(i.USART_GetFlagStatus) for USART_GetFlagStatus
    usart1.o(i.Usart1_SendChar) refers to stm32f10x_usart.o(i.USART_SendData) for USART_SendData
    usart1.o(i.Usart1_SendString) refers to usart1.o(i.Usart1_SendChar) for Usart1_SendChar
    delay.o(i.delay_init) refers to delay.o(.data) for fac_us
    delay.o(i.delay_ms) refers to delay.o(i.delay_us) for delay_us
    delay.o(i.delay_s) refers to delay.o(i.delay_ms) for delay_ms
    delay.o(i.delay_us) refers to delay.o(.data) for fac_us
    delay.o(i.delay_xms) refers to delay.o(i.delay_ms) for delay_ms
    usart3.o(i.USART3_ReceiveChar) refers to stm32f10x_usart.o(i.USART_GetFlagStatus) for USART_GetFlagStatus
    usart3.o(i.USART3_ReceiveChar) refers to stm32f10x_usart.o(i.USART_ReceiveData) for USART_ReceiveData
    usart3.o(i.USART3_SendChar) refers to stm32f10x_usart.o(i.USART_GetFlagStatus) for USART_GetFlagStatus
    usart3.o(i.USART3_SendChar) refers to stm32f10x_usart.o(i.USART_SendData) for USART_SendData
    usart3.o(i.USART3_SendString) refers to usart3.o(i.USART3_SendChar) for USART3_SendChar
    usart3.o(i.Usart3_Init) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphClockCmd) for RCC_APB2PeriphClockCmd
    usart3.o(i.Usart3_Init) refers to stm32f10x_rcc.o(i.RCC_APB1PeriphClockCmd) for RCC_APB1PeriphClockCmd
    usart3.o(i.Usart3_Init) refers to stm32f10x_gpio.o(i.GPIO_Init) for GPIO_Init
    usart3.o(i.Usart3_Init) refers to stm32f10x_usart.o(i.USART_Init) for USART_Init
    usart3.o(i.Usart3_Init) refers to stm32f10x_usart.o(i.USART_Cmd) for USART_Cmd
    usart2.o(i.Usart2_Init) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphClockCmd) for RCC_APB2PeriphClockCmd
    usart2.o(i.Usart2_Init) refers to stm32f10x_rcc.o(i.RCC_APB1PeriphClockCmd) for RCC_APB1PeriphClockCmd
    usart2.o(i.Usart2_Init) refers to stm32f10x_gpio.o(i.GPIO_Init) for GPIO_Init
    usart2.o(i.Usart2_Init) refers to stm32f10x_usart.o(i.USART_Init) for USART_Init
    usart2.o(i.Usart2_Init) refers to stm32f10x_usart.o(i.USART_Cmd) for USART_Cmd
    usart2.o(i.Usart2_ReceiveChar) refers to stm32f10x_usart.o(i.USART_GetFlagStatus) for USART_GetFlagStatus
    usart2.o(i.Usart2_ReceiveChar) refers to stm32f10x_usart.o(i.USART_ReceiveData) for USART_ReceiveData
    usart2.o(i.Usart2_SendChar) refers to stm32f10x_usart.o(i.USART_GetFlagStatus) for USART_GetFlagStatus
    usart2.o(i.Usart2_SendChar) refers to stm32f10x_usart.o(i.USART_SendData) for USART_SendData
    usart2.o(i.Usart2_SendString) refers to usart2.o(i.Usart2_SendChar) for Usart2_SendChar
    usart5.o(i.Usart5_Init) refers (Special) to use_no_semi_2.o(.text) for __use_no_semihosting
    usart5.o(i.Usart5_Init) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphClockCmd) for RCC_APB2PeriphClockCmd
    usart5.o(i.Usart5_Init) refers to stm32f10x_rcc.o(i.RCC_APB1PeriphClockCmd) for RCC_APB1PeriphClockCmd
    usart5.o(i.Usart5_Init) refers to stm32f10x_gpio.o(i.GPIO_Init) for GPIO_Init
    usart5.o(i.Usart5_Init) refers to stm32f10x_usart.o(i.USART_Init) for USART_Init
    usart5.o(i.Usart5_Init) refers to stm32f10x_usart.o(i.USART_Cmd) for USART_Cmd
    usart5.o(i.Usart5_ReceiveChar) refers (Special) to use_no_semi_2.o(.text) for __use_no_semihosting
    usart5.o(i.Usart5_ReceiveChar) refers to stm32f10x_usart.o(i.USART_GetFlagStatus) for USART_GetFlagStatus
    usart5.o(i.Usart5_ReceiveChar) refers to stm32f10x_usart.o(i.USART_ReceiveData) for USART_ReceiveData
    usart5.o(i.Usart5_SendChar) refers (Special) to use_no_semi_2.o(.text) for __use_no_semihosting
    usart5.o(i.Usart5_SendChar) refers to stm32f10x_usart.o(i.USART_GetFlagStatus) for USART_GetFlagStatus
    usart5.o(i.Usart5_SendChar) refers to stm32f10x_usart.o(i.USART_SendData) for USART_SendData
    usart5.o(i.Usart5_SendString) refers (Special) to use_no_semi_2.o(.text) for __use_no_semihosting
    usart5.o(i.Usart5_SendString) refers to usart5.o(i.Usart5_SendChar) for Usart5_SendChar
    usart5.o(i._sys_exit) refers (Special) to use_no_semi_2.o(.text) for __use_no_semihosting
    usart5.o(i.fputc) refers (Special) to use_no_semi_2.o(.text) for __use_no_semihosting
    usart5.o(.data) refers (Special) to use_no_semi_2.o(.text) for __use_no_semihosting
    iic.o(i.IIC_Ack) refers to delay.o(i.delay_us) for delay_us
    iic.o(i.IIC_Init) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphClockCmd) for RCC_APB2PeriphClockCmd
    iic.o(i.IIC_Init) refers to stm32f10x_gpio.o(i.GPIO_Init) for GPIO_Init
    iic.o(i.IIC_NAck) refers to delay.o(i.delay_us) for delay_us
    iic.o(i.IIC_Read_Byte) refers to delay.o(i.delay_us) for delay_us
    iic.o(i.IIC_Read_Byte) refers to iic.o(i.IIC_NAck) for IIC_NAck
    iic.o(i.IIC_Read_Byte) refers to iic.o(i.IIC_Ack) for IIC_Ack
    iic.o(i.IIC_Send_Byte) refers to delay.o(i.delay_us) for delay_us
    iic.o(i.IIC_Start) refers to delay.o(i.delay_us) for delay_us
    iic.o(i.IIC_Stop) refers to delay.o(i.delay_us) for delay_us
    iic.o(i.IIC_Wait_Ack) refers to delay.o(i.delay_us) for delay_us
    iic.o(i.IIC_Wait_Ack) refers to iic.o(i.IIC_Stop) for IIC_Stop
    main.o(i.init) refers to delay.o(i.delay_init) for delay_init
    main.o(i.init) refers to oled.o(i.OLED_Init) for OLED_Init
    main.o(i.init) refers to pca9685.o(i.PCA9685_Init) for PCA9685_Init
    main.o(i.init) refers to tb6612.o(i.MOTOR_Init) for MOTOR_Init
    main.o(i.init) refers to usart2.o(i.Usart2_Init) for Usart2_Init
    main.o(i.init) refers to usart5.o(i.Usart5_Init) for Usart5_Init
    main.o(i.init) refers to encoder.o(i.Encoder_Init) for Encoder_Init
    main.o(i.init) refers to pid_control.o(i.PID_Control_Init) for PID_Control_Init
    main.o(i.main) refers to main.o(i.init) for init
    main.o(i.main) refers to pid_control.o(i.Speed_Control_Start) for Speed_Control_Start
    use_no_semi_2.o(.text) refers (Special) to use_no_semi.o(.text) for __use_no_semihosting_swi
    __main.o(!!!main) refers to __rtentry.o(.ARM.Collect$$rtentry$$00000000) for __rt_entry
    d2f.o(x$fpl$d2f) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    d2f.o(x$fpl$d2f) refers to fretinf.o(x$fpl$fretinf) for __fpl_fretinf
    d2f.o(x$fpl$d2f) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    daddsub_clz.o(x$fpl$dadd) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    daddsub_clz.o(x$fpl$dadd) refers to daddsub_clz.o(x$fpl$dsub) for _dsub1
    daddsub_clz.o(x$fpl$dadd) refers to dretinf.o(x$fpl$dretinf) for __fpl_dretinf
    daddsub_clz.o(x$fpl$dadd) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    daddsub_clz.o(x$fpl$drsb) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    daddsub_clz.o(x$fpl$drsb) refers to daddsub_clz.o(x$fpl$dadd) for _dadd1
    daddsub_clz.o(x$fpl$drsb) refers to daddsub_clz.o(x$fpl$dsub) for _dsub1
    daddsub_clz.o(x$fpl$dsub) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    daddsub_clz.o(x$fpl$dsub) refers to daddsub_clz.o(x$fpl$dadd) for _dadd1
    daddsub_clz.o(x$fpl$dsub) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    ddiv.o(x$fpl$drdiv) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    ddiv.o(x$fpl$drdiv) refers to ddiv.o(x$fpl$ddiv) for ddiv_entry
    ddiv.o(x$fpl$ddiv) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    ddiv.o(x$fpl$ddiv) refers to dretinf.o(x$fpl$dretinf) for __fpl_dretinf
    ddiv.o(x$fpl$ddiv) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    dfixu.o(x$fpl$dfixu) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dfixu.o(x$fpl$dfixu) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    dfixu.o(x$fpl$dfixur) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dfixu.o(x$fpl$dfixur) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    dflt_clz.o(x$fpl$dfltu) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dflt_clz.o(x$fpl$dflt) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dflt_clz.o(x$fpl$dfltn) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dmul.o(x$fpl$dmul) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dmul.o(x$fpl$dmul) refers to dretinf.o(x$fpl$dretinf) for __fpl_dretinf
    dmul.o(x$fpl$dmul) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    f2d.o(x$fpl$f2d) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    f2d.o(x$fpl$f2d) refers to fnaninf.o(x$fpl$fnaninf) for __fpl_fnaninf
    f2d.o(x$fpl$f2d) refers to dretinf.o(x$fpl$dretinf) for __fpl_dretinf
    faddsub_clz.o(x$fpl$fadd) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    faddsub_clz.o(x$fpl$fadd) refers to faddsub_clz.o(x$fpl$fsub) for _fsub1
    faddsub_clz.o(x$fpl$fadd) refers to fretinf.o(x$fpl$fretinf) for __fpl_fretinf
    faddsub_clz.o(x$fpl$fadd) refers to fnaninf.o(x$fpl$fnaninf) for __fpl_fnaninf
    faddsub_clz.o(x$fpl$frsb) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    faddsub_clz.o(x$fpl$frsb) refers to faddsub_clz.o(x$fpl$fadd) for _fadd1
    faddsub_clz.o(x$fpl$frsb) refers to faddsub_clz.o(x$fpl$fsub) for _fsub1
    faddsub_clz.o(x$fpl$fsub) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    faddsub_clz.o(x$fpl$fsub) refers to faddsub_clz.o(x$fpl$fadd) for _fadd1
    faddsub_clz.o(x$fpl$fsub) refers to fnaninf.o(x$fpl$fnaninf) for __fpl_fnaninf
    fdiv.o(x$fpl$frdiv) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fdiv.o(x$fpl$frdiv) refers to fdiv.o(x$fpl$fdiv) for _fdiv1
    fdiv.o(x$fpl$fdiv) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fdiv.o(x$fpl$fdiv) refers to fretinf.o(x$fpl$fretinf) for __fpl_fretinf
    fdiv.o(x$fpl$fdiv) refers to fnaninf.o(x$fpl$fnaninf) for __fpl_fnaninf
    feqf.o(x$fpl$feqf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    feqf.o(x$fpl$feqf) refers to fnaninf.o(x$fpl$fnaninf) for __fpl_fnaninf
    feqf.o(x$fpl$feqf) refers to fcmpi.o(x$fpl$fcmpinf) for __fpl_fcmp_Inf
    ffix.o(x$fpl$ffix) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    ffix.o(x$fpl$ffix) refers to fnaninf.o(x$fpl$fnaninf) for __fpl_fnaninf
    ffix.o(x$fpl$ffixr) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    ffix.o(x$fpl$ffixr) refers to fnaninf.o(x$fpl$fnaninf) for __fpl_fnaninf
    fflt_clz.o(x$fpl$ffltu) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fflt_clz.o(x$fpl$fflt) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fflt_clz.o(x$fpl$ffltn) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fmul.o(x$fpl$fmul) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fmul.o(x$fpl$fmul) refers to fretinf.o(x$fpl$fretinf) for __fpl_fretinf
    fmul.o(x$fpl$fmul) refers to fnaninf.o(x$fpl$fnaninf) for __fpl_fnaninf
    frleqf.o(x$fpl$frleqf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    frleqf.o(x$fpl$frleqf) refers to fleqf.o(x$fpl$fleqf) for __fpl_fcmple_InfNaN
    floor.o(i.__softfp_floor) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    floor.o(i.__softfp_floor) refers to floor.o(i.floor) for floor
    floor.o(i.floor) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    floor.o(i.floor) refers to daddsub_clz.o(x$fpl$dadd) for __aeabi_dadd
    floor.o(i.floor) refers to drleqf.o(x$fpl$drleqf) for __aeabi_cdrcmple
    floor.o(i.floor) refers to daddsub_clz.o(x$fpl$drsb) for __aeabi_drsub
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$0000000A) for __rt_entry_li
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) for __rt_entry_main
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$0000000C) for __rt_entry_postli_1
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$00000009) for __rt_entry_postsh_1
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$00000002) for __rt_entry_presh_1
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry4.o(.ARM.Collect$$rtentry$$00000004) for __rt_entry_sh
    dnaninf.o(x$fpl$dnaninf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dretinf.o(x$fpl$dretinf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    drleqf.o(x$fpl$drleqf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    drleqf.o(x$fpl$drleqf) refers to dleqf.o(x$fpl$dleqf) for __fpl_dcmple_InfNaN
    fcmpi.o(x$fpl$fcmpinf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fleqf.o(x$fpl$fleqf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fleqf.o(x$fpl$fleqf) refers to fnaninf.o(x$fpl$fnaninf) for __fpl_fnaninf
    fleqf.o(x$fpl$fleqf) refers to fcmpi.o(x$fpl$fcmpinf) for __fpl_fcmp_Inf
    fnaninf.o(x$fpl$fnaninf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fretinf.o(x$fpl$fretinf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    __rtentry2.o(.ARM.Collect$$rtentry$$00000008) refers to boardinit2.o(.text) for _platform_post_stackheap_init
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000A) refers to libinit.o(.ARM.Collect$$libinit$$00000000) for __rt_lib_init
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000B) refers to boardinit3.o(.text) for _platform_post_lib_init
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) refers to main.o(i.main) for main
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) refers to exit.o(.text) for exit
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$00000001) for .ARM.Collect$$rtentry$$00000001
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$00000008) for .ARM.Collect$$rtentry$$00000008
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$0000000A) for .ARM.Collect$$rtentry$$0000000A
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$0000000B) for .ARM.Collect$$rtentry$$0000000B
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) for .ARM.Collect$$rtentry$$0000000D
    __rtentry4.o(.ARM.Collect$$rtentry$$00000004) refers to sys_stackheap_outer.o(.text) for __user_setup_stackheap
    __rtentry4.o(.ARM.exidx) refers to __rtentry4.o(.ARM.Collect$$rtentry$$00000004) for .ARM.Collect$$rtentry$$00000004
    dleqf.o(x$fpl$dleqf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dleqf.o(x$fpl$dleqf) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    dleqf.o(x$fpl$dleqf) refers to dcmpi.o(x$fpl$dcmpinf) for __fpl_dcmp_Inf
    sys_stackheap_outer.o(.text) refers to libspace.o(.text) for __user_perproc_libspace
    sys_stackheap_outer.o(.text) refers to startup_stm32f10x_hd.o(.text) for __user_initial_stackheap
    exit.o(.text) refers to rtexit.o(.ARM.Collect$$rtexit$$00000000) for __rt_exit
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000002E) for __rt_lib_init_alloca_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000002C) for __rt_lib_init_argv_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000001B) for __rt_lib_init_atexit_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000021) for __rt_lib_init_clock_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000032) for __rt_lib_init_cpp_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000030) for __rt_lib_init_exceptions_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000002) for __rt_lib_init_fp_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000001F) for __rt_lib_init_fp_trap_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000023) for __rt_lib_init_getenv_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000000A) for __rt_lib_init_heap_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000011) for __rt_lib_init_lc_collate_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000013) for __rt_lib_init_lc_ctype_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000015) for __rt_lib_init_lc_monetary_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000017) for __rt_lib_init_lc_numeric_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000019) for __rt_lib_init_lc_time_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000004) for __rt_lib_init_preinit_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000000E) for __rt_lib_init_rand_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000033) for __rt_lib_init_return
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000001D) for __rt_lib_init_signal_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000025) for __rt_lib_init_stdio_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000000C) for __rt_lib_init_user_alloc_1
    dcmpi.o(x$fpl$dcmpinf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    libspace.o(.text) refers to libspace.o(.bss) for __libspace_start
    rtexit.o(.ARM.Collect$$rtexit$$00000000) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000004) for __rt_exit_exit
    rtexit.o(.ARM.Collect$$rtexit$$00000000) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000003) for __rt_exit_ls
    rtexit.o(.ARM.Collect$$rtexit$$00000000) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000002) for __rt_exit_prels_1
    rtexit.o(.ARM.exidx) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000004) for __rt_exit_exit
    rtexit.o(.ARM.exidx) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000003) for __rt_exit_ls
    rtexit.o(.ARM.exidx) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000002) for __rt_exit_prels_1
    rtexit.o(.ARM.exidx) refers to rtexit.o(.ARM.Collect$$rtexit$$00000000) for .ARM.Collect$$rtexit$$00000000
    libinit2.o(.ARM.Collect$$libinit$$00000010) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000012) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000014) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000016) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000018) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000026) refers to argv_veneer.o(.emb_text) for __ARM_argv_veneer
    libinit2.o(.ARM.Collect$$libinit$$00000027) refers to argv_veneer.o(.emb_text) for __ARM_argv_veneer
    rtexit2.o(.ARM.Collect$$rtexit$$00000003) refers to libshutdown.o(.ARM.Collect$$libshutdown$$00000000) for __rt_lib_shutdown
    rtexit2.o(.ARM.Collect$$rtexit$$00000004) refers to usart5.o(i._sys_exit) for _sys_exit
    rtexit2.o(.ARM.exidx) refers to rtexit2.o(.ARM.Collect$$rtexit$$00000001) for .ARM.Collect$$rtexit$$00000001
    rtexit2.o(.ARM.exidx) refers to rtexit2.o(.ARM.Collect$$rtexit$$00000003) for .ARM.Collect$$rtexit$$00000003
    rtexit2.o(.ARM.exidx) refers to rtexit2.o(.ARM.Collect$$rtexit$$00000004) for .ARM.Collect$$rtexit$$00000004
    argv_veneer.o(.emb_text) refers to no_argv.o(.text) for __ARM_get_argv
    _get_argv_nomalloc.o(.text) refers (Special) to hrguard.o(.text) for __heap_region$guard
    _get_argv_nomalloc.o(.text) refers to defsig_rtmem_outer.o(.text) for __rt_SIGRTMEM
    _get_argv_nomalloc.o(.text) refers to sys_command.o(.text) for _sys_command_string
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000004) for __rt_lib_shutdown_cpp_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000002) for __rt_lib_shutdown_fini_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000009) for __rt_lib_shutdown_fp_trap_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000011) for __rt_lib_shutdown_heap_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000012) for __rt_lib_shutdown_return
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$0000000C) for __rt_lib_shutdown_signal_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000006) for __rt_lib_shutdown_stdio_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$0000000E) for __rt_lib_shutdown_user_alloc_1
    sys_command.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_command.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    defsig_rtmem_outer.o(.text) refers to defsig_rtmem_inner.o(.text) for __rt_SIGRTMEM_inner
    defsig_rtmem_outer.o(.text) refers to defsig_exit.o(.text) for __sig_exit
    defsig_rtmem_formal.o(.text) refers to rt_raise.o(.text) for __rt_raise
    rt_raise.o(.text) refers to __raise.o(.text) for __raise
    rt_raise.o(.text) refers to usart5.o(i._sys_exit) for _sys_exit
    defsig_exit.o(.text) refers to usart5.o(i._sys_exit) for _sys_exit
    defsig_rtmem_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    __raise.o(.text) refers to defsig.o(CL$$defsig) for __default_signal_handler
    defsig_general.o(.text) refers to sys_wrch.o(.text) for _ttywrch
    sys_wrch.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_wrch.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    defsig.o(CL$$defsig) refers to defsig_rtmem_inner.o(.text) for __rt_SIGRTMEM_inner
    defsig_abrt_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_fpe_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_rtred_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_stak_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_pvfn_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_cppl_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_segv_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_other.o(.text) refers to defsig_general.o(.text) for __default_signal_display


==============================================================================

Removing Unused input sections from the image.

    Removing system_stm32f10x.o(i.SystemCoreClockUpdate), (164 bytes).
    Removing system_stm32f10x.o(.data), (20 bytes).
    Removing core_cm3.o(.emb_text), (32 bytes).
    Removing misc.o(i.NVIC_PriorityGroupConfig), (20 bytes).
    Removing misc.o(i.NVIC_SetVectorTable), (20 bytes).
    Removing misc.o(i.NVIC_SystemLPConfig), (32 bytes).
    Removing misc.o(i.SysTick_CLKSourceConfig), (40 bytes).
    Removing stm32f10x_adc.o(i.ADC_AnalogWatchdogCmd), (20 bytes).
    Removing stm32f10x_adc.o(i.ADC_AnalogWatchdogSingleChannelConfig), (16 bytes).
    Removing stm32f10x_adc.o(i.ADC_AnalogWatchdogThresholdsConfig), (6 bytes).
    Removing stm32f10x_adc.o(i.ADC_AutoInjectedConvCmd), (22 bytes).
    Removing stm32f10x_adc.o(i.ADC_ClearFlag), (6 bytes).
    Removing stm32f10x_adc.o(i.ADC_ClearITPendingBit), (10 bytes).
    Removing stm32f10x_adc.o(i.ADC_Cmd), (22 bytes).
    Removing stm32f10x_adc.o(i.ADC_DMACmd), (22 bytes).
    Removing stm32f10x_adc.o(i.ADC_DeInit), (92 bytes).
    Removing stm32f10x_adc.o(i.ADC_DiscModeChannelCountConfig), (24 bytes).
    Removing stm32f10x_adc.o(i.ADC_DiscModeCmd), (22 bytes).
    Removing stm32f10x_adc.o(i.ADC_ExternalTrigConvCmd), (22 bytes).
    Removing stm32f10x_adc.o(i.ADC_ExternalTrigInjectedConvCmd), (22 bytes).
    Removing stm32f10x_adc.o(i.ADC_ExternalTrigInjectedConvConfig), (16 bytes).
    Removing stm32f10x_adc.o(i.ADC_GetCalibrationStatus), (20 bytes).
    Removing stm32f10x_adc.o(i.ADC_GetConversionValue), (8 bytes).
    Removing stm32f10x_adc.o(i.ADC_GetDualModeConversionValue), (12 bytes).
    Removing stm32f10x_adc.o(i.ADC_GetFlagStatus), (18 bytes).
    Removing stm32f10x_adc.o(i.ADC_GetITStatus), (36 bytes).
    Removing stm32f10x_adc.o(i.ADC_GetInjectedConversionValue), (28 bytes).
    Removing stm32f10x_adc.o(i.ADC_GetResetCalibrationStatus), (20 bytes).
    Removing stm32f10x_adc.o(i.ADC_GetSoftwareStartConvStatus), (20 bytes).
    Removing stm32f10x_adc.o(i.ADC_GetSoftwareStartInjectedConvCmdStatus), (20 bytes).
    Removing stm32f10x_adc.o(i.ADC_ITConfig), (24 bytes).
    Removing stm32f10x_adc.o(i.ADC_Init), (80 bytes).
    Removing stm32f10x_adc.o(i.ADC_InjectedChannelConfig), (130 bytes).
    Removing stm32f10x_adc.o(i.ADC_InjectedDiscModeCmd), (22 bytes).
    Removing stm32f10x_adc.o(i.ADC_InjectedSequencerLengthConfig), (24 bytes).
    Removing stm32f10x_adc.o(i.ADC_RegularChannelConfig), (184 bytes).
    Removing stm32f10x_adc.o(i.ADC_ResetCalibration), (10 bytes).
    Removing stm32f10x_adc.o(i.ADC_SetInjectedOffset), (20 bytes).
    Removing stm32f10x_adc.o(i.ADC_SoftwareStartConvCmd), (22 bytes).
    Removing stm32f10x_adc.o(i.ADC_SoftwareStartInjectedConvCmd), (22 bytes).
    Removing stm32f10x_adc.o(i.ADC_StartCalibration), (10 bytes).
    Removing stm32f10x_adc.o(i.ADC_StructInit), (18 bytes).
    Removing stm32f10x_adc.o(i.ADC_TempSensorVrefintCmd), (36 bytes).
    Removing stm32f10x_bkp.o(i.BKP_ClearFlag), (20 bytes).
    Removing stm32f10x_bkp.o(i.BKP_ClearITPendingBit), (20 bytes).
    Removing stm32f10x_bkp.o(i.BKP_DeInit), (16 bytes).
    Removing stm32f10x_bkp.o(i.BKP_GetFlagStatus), (12 bytes).
    Removing stm32f10x_bkp.o(i.BKP_GetITStatus), (12 bytes).
    Removing stm32f10x_bkp.o(i.BKP_ITConfig), (12 bytes).
    Removing stm32f10x_bkp.o(i.BKP_RTCOutputConfig), (28 bytes).
    Removing stm32f10x_bkp.o(i.BKP_ReadBackupRegister), (28 bytes).
    Removing stm32f10x_bkp.o(i.BKP_SetRTCCalibrationValue), (28 bytes).
    Removing stm32f10x_bkp.o(i.BKP_TamperPinCmd), (12 bytes).
    Removing stm32f10x_bkp.o(i.BKP_TamperPinLevelConfig), (12 bytes).
    Removing stm32f10x_bkp.o(i.BKP_WriteBackupRegister), (28 bytes).
    Removing stm32f10x_can.o(i.CAN_CancelTransmit), (48 bytes).
    Removing stm32f10x_can.o(i.CAN_ClearFlag), (56 bytes).
    Removing stm32f10x_can.o(i.CAN_ClearITPendingBit), (168 bytes).
    Removing stm32f10x_can.o(i.CAN_DBGFreeze), (22 bytes).
    Removing stm32f10x_can.o(i.CAN_DeInit), (56 bytes).
    Removing stm32f10x_can.o(i.CAN_FIFORelease), (22 bytes).
    Removing stm32f10x_can.o(i.CAN_FilterInit), (264 bytes).
    Removing stm32f10x_can.o(i.CAN_GetFlagStatus), (120 bytes).
    Removing stm32f10x_can.o(i.CAN_GetITStatus), (288 bytes).
    Removing stm32f10x_can.o(i.CAN_GetLSBTransmitErrorCounter), (12 bytes).
    Removing stm32f10x_can.o(i.CAN_GetLastErrorCode), (12 bytes).
    Removing stm32f10x_can.o(i.CAN_GetReceiveErrorCounter), (10 bytes).
    Removing stm32f10x_can.o(i.CAN_ITConfig), (18 bytes).
    Removing stm32f10x_can.o(i.CAN_Init), (276 bytes).
    Removing stm32f10x_can.o(i.CAN_MessagePending), (30 bytes).
    Removing stm32f10x_can.o(i.CAN_OperatingModeRequest), (162 bytes).
    Removing stm32f10x_can.o(i.CAN_Receive), (240 bytes).
    Removing stm32f10x_can.o(i.CAN_SlaveStartBank), (52 bytes).
    Removing stm32f10x_can.o(i.CAN_Sleep), (30 bytes).
    Removing stm32f10x_can.o(i.CAN_StructInit), (32 bytes).
    Removing stm32f10x_can.o(i.CAN_TTComModeCmd), (118 bytes).
    Removing stm32f10x_can.o(i.CAN_Transmit), (294 bytes).
    Removing stm32f10x_can.o(i.CAN_TransmitStatus), (160 bytes).
    Removing stm32f10x_can.o(i.CAN_WakeUp), (48 bytes).
    Removing stm32f10x_can.o(i.CheckITStatus), (18 bytes).
    Removing stm32f10x_cec.o(i.CEC_ClearFlag), (36 bytes).
    Removing stm32f10x_cec.o(i.CEC_ClearITPendingBit), (36 bytes).
    Removing stm32f10x_cec.o(i.CEC_Cmd), (32 bytes).
    Removing stm32f10x_cec.o(i.CEC_DeInit), (22 bytes).
    Removing stm32f10x_cec.o(i.CEC_EndOfMessageCmd), (12 bytes).
    Removing stm32f10x_cec.o(i.CEC_GetFlagStatus), (48 bytes).
    Removing stm32f10x_cec.o(i.CEC_GetITStatus), (40 bytes).
    Removing stm32f10x_cec.o(i.CEC_ITConfig), (12 bytes).
    Removing stm32f10x_cec.o(i.CEC_Init), (32 bytes).
    Removing stm32f10x_cec.o(i.CEC_OwnAddressConfig), (12 bytes).
    Removing stm32f10x_cec.o(i.CEC_ReceiveDataByte), (12 bytes).
    Removing stm32f10x_cec.o(i.CEC_SendDataByte), (12 bytes).
    Removing stm32f10x_cec.o(i.CEC_SetPrescaler), (12 bytes).
    Removing stm32f10x_cec.o(i.CEC_StartOfMessage), (12 bytes).
    Removing stm32f10x_crc.o(i.CRC_CalcBlockCRC), (36 bytes).
    Removing stm32f10x_crc.o(i.CRC_CalcCRC), (16 bytes).
    Removing stm32f10x_crc.o(i.CRC_GetCRC), (12 bytes).
    Removing stm32f10x_crc.o(i.CRC_GetIDRegister), (12 bytes).
    Removing stm32f10x_crc.o(i.CRC_ResetDR), (12 bytes).
    Removing stm32f10x_crc.o(i.CRC_SetIDRegister), (12 bytes).
    Removing stm32f10x_dac.o(i.DAC_Cmd), (40 bytes).
    Removing stm32f10x_dac.o(i.DAC_DMACmd), (44 bytes).
    Removing stm32f10x_dac.o(i.DAC_DeInit), (22 bytes).
    Removing stm32f10x_dac.o(i.DAC_DualSoftwareTriggerCmd), (36 bytes).
    Removing stm32f10x_dac.o(i.DAC_GetDataOutputValue), (36 bytes).
    Removing stm32f10x_dac.o(i.DAC_Init), (52 bytes).
    Removing stm32f10x_dac.o(i.DAC_SetChannel1Data), (32 bytes).
    Removing stm32f10x_dac.o(i.DAC_SetChannel2Data), (32 bytes).
    Removing stm32f10x_dac.o(i.DAC_SetDualChannelData), (36 bytes).
    Removing stm32f10x_dac.o(i.DAC_SoftwareTriggerCmd), (44 bytes).
    Removing stm32f10x_dac.o(i.DAC_StructInit), (12 bytes).
    Removing stm32f10x_dac.o(i.DAC_WaveGenerationCmd), (40 bytes).
    Removing stm32f10x_dbgmcu.o(i.DBGMCU_Config), (32 bytes).
    Removing stm32f10x_dbgmcu.o(i.DBGMCU_GetDEVID), (16 bytes).
    Removing stm32f10x_dbgmcu.o(i.DBGMCU_GetREVID), (12 bytes).
    Removing stm32f10x_dma.o(i.DMA_ClearFlag), (28 bytes).
    Removing stm32f10x_dma.o(i.DMA_ClearITPendingBit), (28 bytes).
    Removing stm32f10x_dma.o(i.DMA_Cmd), (24 bytes).
    Removing stm32f10x_dma.o(i.DMA_DeInit), (332 bytes).
    Removing stm32f10x_dma.o(i.DMA_GetCurrDataCounter), (8 bytes).
    Removing stm32f10x_dma.o(i.DMA_GetFlagStatus), (44 bytes).
    Removing stm32f10x_dma.o(i.DMA_GetITStatus), (44 bytes).
    Removing stm32f10x_dma.o(i.DMA_ITConfig), (18 bytes).
    Removing stm32f10x_dma.o(i.DMA_Init), (60 bytes).
    Removing stm32f10x_dma.o(i.DMA_SetCurrDataCounter), (4 bytes).
    Removing stm32f10x_dma.o(i.DMA_StructInit), (26 bytes).
    Removing stm32f10x_exti.o(i.EXTI_ClearFlag), (12 bytes).
    Removing stm32f10x_exti.o(i.EXTI_ClearITPendingBit), (12 bytes).
    Removing stm32f10x_exti.o(i.EXTI_DeInit), (36 bytes).
    Removing stm32f10x_exti.o(i.EXTI_GenerateSWInterrupt), (16 bytes).
    Removing stm32f10x_exti.o(i.EXTI_GetFlagStatus), (24 bytes).
    Removing stm32f10x_exti.o(i.EXTI_GetITStatus), (40 bytes).
    Removing stm32f10x_exti.o(i.EXTI_Init), (148 bytes).
    Removing stm32f10x_exti.o(i.EXTI_StructInit), (16 bytes).
    Removing stm32f10x_flash.o(i.FLASH_ClearFlag), (12 bytes).
    Removing stm32f10x_flash.o(i.FLASH_EnableWriteProtection), (196 bytes).
    Removing stm32f10x_flash.o(i.FLASH_EraseAllBank1Pages), (72 bytes).
    Removing stm32f10x_flash.o(i.FLASH_EraseAllPages), (72 bytes).
    Removing stm32f10x_flash.o(i.FLASH_EraseOptionBytes), (168 bytes).
    Removing stm32f10x_flash.o(i.FLASH_ErasePage), (76 bytes).
    Removing stm32f10x_flash.o(i.FLASH_GetBank1Status), (52 bytes).
    Removing stm32f10x_flash.o(i.FLASH_GetFlagStatus), (48 bytes).
    Removing stm32f10x_flash.o(i.FLASH_GetPrefetchBufferStatus), (24 bytes).
    Removing stm32f10x_flash.o(i.FLASH_GetReadOutProtectionStatus), (24 bytes).
    Removing stm32f10x_flash.o(i.FLASH_GetStatus), (52 bytes).
    Removing stm32f10x_flash.o(i.FLASH_GetUserOptionByte), (12 bytes).
    Removing stm32f10x_flash.o(i.FLASH_GetWriteProtectionOptionByte), (12 bytes).
    Removing stm32f10x_flash.o(i.FLASH_HalfCycleAccessCmd), (28 bytes).
    Removing stm32f10x_flash.o(i.FLASH_ITConfig), (32 bytes).
    Removing stm32f10x_flash.o(i.FLASH_Lock), (20 bytes).
    Removing stm32f10x_flash.o(i.FLASH_LockBank1), (20 bytes).
    Removing stm32f10x_flash.o(i.FLASH_PrefetchBufferCmd), (28 bytes).
    Removing stm32f10x_flash.o(i.FLASH_ProgramHalfWord), (64 bytes).
    Removing stm32f10x_flash.o(i.FLASH_ProgramOptionByteData), (84 bytes).
    Removing stm32f10x_flash.o(i.FLASH_ProgramWord), (108 bytes).
    Removing stm32f10x_flash.o(i.FLASH_ReadOutProtection), (172 bytes).
    Removing stm32f10x_flash.o(i.FLASH_SetLatency), (24 bytes).
    Removing stm32f10x_flash.o(i.FLASH_Unlock), (24 bytes).
    Removing stm32f10x_flash.o(i.FLASH_UnlockBank1), (24 bytes).
    Removing stm32f10x_flash.o(i.FLASH_UserOptionByteConfig), (104 bytes).
    Removing stm32f10x_flash.o(i.FLASH_WaitForLastBank1Operation), (38 bytes).
    Removing stm32f10x_flash.o(i.FLASH_WaitForLastOperation), (38 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_ClearFlag), (64 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_ClearITPendingBit), (72 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_GetECC), (28 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_GetFlagStatus), (56 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_GetITStatus), (68 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_ITConfig), (128 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_NANDCmd), (92 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_NANDDeInit), (68 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_NANDECCCmd), (92 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_NANDInit), (136 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_NANDStructInit), (54 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_NORSRAMCmd), (52 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_NORSRAMDeInit), (54 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_NORSRAMInit), (230 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_NORSRAMStructInit), (114 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_PCCARDCmd), (48 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_PCCARDDeInit), (40 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_PCCARDInit), (132 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_PCCARDStructInit), (60 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_AFIODeInit), (20 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_DeInit), (200 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_ETH_MediaInterfaceConfig), (12 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_EXTILineConfig), (64 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_EventOutputCmd), (12 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_EventOutputConfig), (32 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_PinLockConfig), (18 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_ReadInputData), (8 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_ReadInputDataBit), (18 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_ReadOutputData), (8 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_ReadOutputDataBit), (18 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_StructInit), (16 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_Write), (4 bytes).
    Removing stm32f10x_i2c.o(i.I2C_ARPCmd), (24 bytes).
    Removing stm32f10x_i2c.o(i.I2C_AcknowledgeConfig), (24 bytes).
    Removing stm32f10x_i2c.o(i.I2C_CalculatePEC), (24 bytes).
    Removing stm32f10x_i2c.o(i.I2C_CheckEvent), (42 bytes).
    Removing stm32f10x_i2c.o(i.I2C_ClearFlag), (12 bytes).
    Removing stm32f10x_i2c.o(i.I2C_ClearITPendingBit), (12 bytes).
    Removing stm32f10x_i2c.o(i.I2C_Cmd), (24 bytes).
    Removing stm32f10x_i2c.o(i.I2C_DMACmd), (24 bytes).
    Removing stm32f10x_i2c.o(i.I2C_DMALastTransferCmd), (24 bytes).
    Removing stm32f10x_i2c.o(i.I2C_DeInit), (56 bytes).
    Removing stm32f10x_i2c.o(i.I2C_DualAddressCmd), (24 bytes).
    Removing stm32f10x_i2c.o(i.I2C_FastModeDutyCycleConfig), (28 bytes).
    Removing stm32f10x_i2c.o(i.I2C_GeneralCallCmd), (24 bytes).
    Removing stm32f10x_i2c.o(i.I2C_GenerateSTART), (24 bytes).
    Removing stm32f10x_i2c.o(i.I2C_GenerateSTOP), (24 bytes).
    Removing stm32f10x_i2c.o(i.I2C_GetFlagStatus), (58 bytes).
    Removing stm32f10x_i2c.o(i.I2C_GetITStatus), (38 bytes).
    Removing stm32f10x_i2c.o(i.I2C_GetLastEvent), (26 bytes).
    Removing stm32f10x_i2c.o(i.I2C_GetPEC), (8 bytes).
    Removing stm32f10x_i2c.o(i.I2C_ITConfig), (18 bytes).
    Removing stm32f10x_i2c.o(i.I2C_Init), (236 bytes).
    Removing stm32f10x_i2c.o(i.I2C_NACKPositionConfig), (28 bytes).
    Removing stm32f10x_i2c.o(i.I2C_OwnAddress2Config), (22 bytes).
    Removing stm32f10x_i2c.o(i.I2C_PECPositionConfig), (28 bytes).
    Removing stm32f10x_i2c.o(i.I2C_ReadRegister), (22 bytes).
    Removing stm32f10x_i2c.o(i.I2C_ReceiveData), (8 bytes).
    Removing stm32f10x_i2c.o(i.I2C_SMBusAlertConfig), (28 bytes).
    Removing stm32f10x_i2c.o(i.I2C_Send7bitAddress), (18 bytes).
    Removing stm32f10x_i2c.o(i.I2C_SendData), (4 bytes).
    Removing stm32f10x_i2c.o(i.I2C_SoftwareResetCmd), (22 bytes).
    Removing stm32f10x_i2c.o(i.I2C_StretchClockCmd), (24 bytes).
    Removing stm32f10x_i2c.o(i.I2C_StructInit), (30 bytes).
    Removing stm32f10x_i2c.o(i.I2C_TransmitPEC), (24 bytes).
    Removing stm32f10x_iwdg.o(i.IWDG_Enable), (16 bytes).
    Removing stm32f10x_iwdg.o(i.IWDG_GetFlagStatus), (24 bytes).
    Removing stm32f10x_iwdg.o(i.IWDG_ReloadCounter), (16 bytes).
    Removing stm32f10x_iwdg.o(i.IWDG_SetPrescaler), (12 bytes).
    Removing stm32f10x_iwdg.o(i.IWDG_SetReload), (12 bytes).
    Removing stm32f10x_iwdg.o(i.IWDG_WriteAccessCmd), (12 bytes).
    Removing stm32f10x_pwr.o(i.PWR_BackupAccessCmd), (12 bytes).
    Removing stm32f10x_pwr.o(i.PWR_ClearFlag), (20 bytes).
    Removing stm32f10x_pwr.o(i.PWR_DeInit), (22 bytes).
    Removing stm32f10x_pwr.o(i.PWR_EnterSTANDBYMode), (52 bytes).
    Removing stm32f10x_pwr.o(i.PWR_EnterSTOPMode), (64 bytes).
    Removing stm32f10x_pwr.o(i.PWR_GetFlagStatus), (24 bytes).
    Removing stm32f10x_pwr.o(i.PWR_PVDCmd), (12 bytes).
    Removing stm32f10x_pwr.o(i.PWR_PVDLevelConfig), (24 bytes).
    Removing stm32f10x_pwr.o(i.PWR_WakeUpPinCmd), (12 bytes).
    Removing stm32f10x_rcc.o(i.RCC_ADCCLKConfig), (24 bytes).
    Removing stm32f10x_rcc.o(i.RCC_AHBPeriphClockCmd), (32 bytes).
    Removing stm32f10x_rcc.o(i.RCC_APB1PeriphResetCmd), (32 bytes).
    Removing stm32f10x_rcc.o(i.RCC_APB2PeriphResetCmd), (32 bytes).
    Removing stm32f10x_rcc.o(i.RCC_AdjustHSICalibrationValue), (24 bytes).
    Removing stm32f10x_rcc.o(i.RCC_BackupResetCmd), (12 bytes).
    Removing stm32f10x_rcc.o(i.RCC_ClearFlag), (20 bytes).
    Removing stm32f10x_rcc.o(i.RCC_ClearITPendingBit), (12 bytes).
    Removing stm32f10x_rcc.o(i.RCC_ClockSecuritySystemCmd), (12 bytes).
    Removing stm32f10x_rcc.o(i.RCC_DeInit), (76 bytes).
    Removing stm32f10x_rcc.o(i.RCC_GetFlagStatus), (60 bytes).
    Removing stm32f10x_rcc.o(i.RCC_GetITStatus), (24 bytes).
    Removing stm32f10x_rcc.o(i.RCC_GetSYSCLKSource), (16 bytes).
    Removing stm32f10x_rcc.o(i.RCC_HCLKConfig), (24 bytes).
    Removing stm32f10x_rcc.o(i.RCC_HSEConfig), (76 bytes).
    Removing stm32f10x_rcc.o(i.RCC_HSICmd), (12 bytes).
    Removing stm32f10x_rcc.o(i.RCC_ITConfig), (32 bytes).
    Removing stm32f10x_rcc.o(i.RCC_LSEConfig), (52 bytes).
    Removing stm32f10x_rcc.o(i.RCC_LSICmd), (12 bytes).
    Removing stm32f10x_rcc.o(i.RCC_MCOConfig), (12 bytes).
    Removing stm32f10x_rcc.o(i.RCC_PCLK1Config), (24 bytes).
    Removing stm32f10x_rcc.o(i.RCC_PCLK2Config), (24 bytes).
    Removing stm32f10x_rcc.o(i.RCC_PLLCmd), (12 bytes).
    Removing stm32f10x_rcc.o(i.RCC_PLLConfig), (28 bytes).
    Removing stm32f10x_rcc.o(i.RCC_RTCCLKCmd), (12 bytes).
    Removing stm32f10x_rcc.o(i.RCC_RTCCLKConfig), (16 bytes).
    Removing stm32f10x_rcc.o(i.RCC_SYSCLKConfig), (24 bytes).
    Removing stm32f10x_rcc.o(i.RCC_USBCLKConfig), (12 bytes).
    Removing stm32f10x_rcc.o(i.RCC_WaitForHSEStartUp), (56 bytes).
    Removing stm32f10x_rtc.o(i.RTC_ClearFlag), (16 bytes).
    Removing stm32f10x_rtc.o(i.RTC_ClearITPendingBit), (16 bytes).
    Removing stm32f10x_rtc.o(i.RTC_EnterConfigMode), (20 bytes).
    Removing stm32f10x_rtc.o(i.RTC_ExitConfigMode), (20 bytes).
    Removing stm32f10x_rtc.o(i.RTC_GetCounter), (20 bytes).
    Removing stm32f10x_rtc.o(i.RTC_GetDivider), (24 bytes).
    Removing stm32f10x_rtc.o(i.RTC_GetFlagStatus), (24 bytes).
    Removing stm32f10x_rtc.o(i.RTC_GetITStatus), (36 bytes).
    Removing stm32f10x_rtc.o(i.RTC_ITConfig), (32 bytes).
    Removing stm32f10x_rtc.o(i.RTC_SetAlarm), (28 bytes).
    Removing stm32f10x_rtc.o(i.RTC_SetCounter), (28 bytes).
    Removing stm32f10x_rtc.o(i.RTC_SetPrescaler), (32 bytes).
    Removing stm32f10x_rtc.o(i.RTC_WaitForLastTask), (20 bytes).
    Removing stm32f10x_rtc.o(i.RTC_WaitForSynchro), (36 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_CEATAITCmd), (16 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_ClearFlag), (12 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_ClearITPendingBit), (12 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_ClockCmd), (12 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_CmdStructInit), (14 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_CommandCompletionCmd), (12 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_DMACmd), (12 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_DataConfig), (48 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_DataStructInit), (20 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_DeInit), (36 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_GetCommandResponse), (12 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_GetDataCounter), (12 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_GetFIFOCount), (12 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_GetFlagStatus), (24 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_GetITStatus), (24 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_GetPowerState), (16 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_GetResponse), (24 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_ITConfig), (32 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_Init), (48 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_ReadData), (12 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_SendCEATACmd), (12 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_SendCommand), (44 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_SendSDIOSuspendCmd), (12 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_SetPowerState), (28 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_SetSDIOOperation), (12 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_SetSDIOReadWaitMode), (12 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_StartSDIOReadWait), (12 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_StopSDIOReadWait), (12 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_StructInit), (16 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_WriteData), (12 bytes).
    Removing stm32f10x_spi.o(i.I2S_Cmd), (24 bytes).
    Removing stm32f10x_spi.o(i.I2S_Init), (232 bytes).
    Removing stm32f10x_spi.o(i.I2S_StructInit), (20 bytes).
    Removing stm32f10x_spi.o(i.SPI_BiDirectionalLineConfig), (28 bytes).
    Removing stm32f10x_spi.o(i.SPI_CalculateCRC), (24 bytes).
    Removing stm32f10x_spi.o(i.SPI_Cmd), (24 bytes).
    Removing stm32f10x_spi.o(i.SPI_DataSizeConfig), (18 bytes).
    Removing stm32f10x_spi.o(i.SPI_GetCRC), (16 bytes).
    Removing stm32f10x_spi.o(i.SPI_GetCRCPolynomial), (6 bytes).
    Removing stm32f10x_spi.o(i.SPI_I2S_ClearFlag), (6 bytes).
    Removing stm32f10x_spi.o(i.SPI_I2S_ClearITPendingBit), (20 bytes).
    Removing stm32f10x_spi.o(i.SPI_I2S_DMACmd), (18 bytes).
    Removing stm32f10x_spi.o(i.SPI_I2S_DeInit), (88 bytes).
    Removing stm32f10x_spi.o(i.SPI_I2S_GetFlagStatus), (18 bytes).
    Removing stm32f10x_spi.o(i.SPI_I2S_GetITStatus), (52 bytes).
    Removing stm32f10x_spi.o(i.SPI_I2S_ITConfig), (32 bytes).
    Removing stm32f10x_spi.o(i.SPI_I2S_ReceiveData), (6 bytes).
    Removing stm32f10x_spi.o(i.SPI_I2S_SendData), (4 bytes).
    Removing stm32f10x_spi.o(i.SPI_Init), (60 bytes).
    Removing stm32f10x_spi.o(i.SPI_NSSInternalSoftwareConfig), (30 bytes).
    Removing stm32f10x_spi.o(i.SPI_SSOutputCmd), (24 bytes).
    Removing stm32f10x_spi.o(i.SPI_StructInit), (24 bytes).
    Removing stm32f10x_spi.o(i.SPI_TransmitCRC), (10 bytes).
    Removing stm32f10x_tim.o(i.TI1_Config), (128 bytes).
    Removing stm32f10x_tim.o(i.TI2_Config), (152 bytes).
    Removing stm32f10x_tim.o(i.TI3_Config), (144 bytes).
    Removing stm32f10x_tim.o(i.TI4_Config), (152 bytes).
    Removing stm32f10x_tim.o(i.TIM_ARRPreloadConfig), (24 bytes).
    Removing stm32f10x_tim.o(i.TIM_BDTRConfig), (32 bytes).
    Removing stm32f10x_tim.o(i.TIM_BDTRStructInit), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_CCPreloadControl), (24 bytes).
    Removing stm32f10x_tim.o(i.TIM_CCxCmd), (30 bytes).
    Removing stm32f10x_tim.o(i.TIM_CCxNCmd), (30 bytes).
    Removing stm32f10x_tim.o(i.TIM_ClearFlag), (6 bytes).
    Removing stm32f10x_tim.o(i.TIM_ClearOC1Ref), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_ClearOC2Ref), (24 bytes).
    Removing stm32f10x_tim.o(i.TIM_ClearOC3Ref), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_ClearOC4Ref), (24 bytes).
    Removing stm32f10x_tim.o(i.TIM_CounterModeConfig), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_DMACmd), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_DMAConfig), (10 bytes).
    Removing stm32f10x_tim.o(i.TIM_DeInit), (488 bytes).
    Removing stm32f10x_tim.o(i.TIM_ETRClockMode1Config), (54 bytes).
    Removing stm32f10x_tim.o(i.TIM_ETRClockMode2Config), (32 bytes).
    Removing stm32f10x_tim.o(i.TIM_ETRConfig), (28 bytes).
    Removing stm32f10x_tim.o(i.TIM_ForcedOC1Config), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_ForcedOC2Config), (26 bytes).
    Removing stm32f10x_tim.o(i.TIM_ForcedOC3Config), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_ForcedOC4Config), (26 bytes).
    Removing stm32f10x_tim.o(i.TIM_GenerateEvent), (4 bytes).
    Removing stm32f10x_tim.o(i.TIM_GetCapture1), (6 bytes).
    Removing stm32f10x_tim.o(i.TIM_GetCapture2), (6 bytes).
    Removing stm32f10x_tim.o(i.TIM_GetCapture3), (6 bytes).
    Removing stm32f10x_tim.o(i.TIM_GetCapture4), (8 bytes).
    Removing stm32f10x_tim.o(i.TIM_GetFlagStatus), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_GetPrescaler), (6 bytes).
    Removing stm32f10x_tim.o(i.TIM_ICInit), (172 bytes).
    Removing stm32f10x_tim.o(i.TIM_ICStructInit), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_ITRxExternalClockConfig), (24 bytes).
    Removing stm32f10x_tim.o(i.TIM_InternalClockConfig), (12 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC1FastConfig), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC1NPolarityConfig), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC1PolarityConfig), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC2FastConfig), (26 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC2NPolarityConfig), (26 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC2PolarityConfig), (26 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC3FastConfig), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC3NPolarityConfig), (26 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC3PolarityConfig), (26 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC4FastConfig), (26 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC4PolarityConfig), (26 bytes).
    Removing stm32f10x_tim.o(i.TIM_OCStructInit), (20 bytes).
    Removing stm32f10x_tim.o(i.TIM_PWMIConfig), (124 bytes).
    Removing stm32f10x_tim.o(i.TIM_PrescalerConfig), (6 bytes).
    Removing stm32f10x_tim.o(i.TIM_SelectCCDMA), (24 bytes).
    Removing stm32f10x_tim.o(i.TIM_SelectCOM), (24 bytes).
    Removing stm32f10x_tim.o(i.TIM_SelectHallSensor), (24 bytes).
    Removing stm32f10x_tim.o(i.TIM_SelectInputTrigger), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_SelectMasterSlaveMode), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_SelectOCxM), (82 bytes).
    Removing stm32f10x_tim.o(i.TIM_SelectOnePulseMode), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_SelectOutputTrigger), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_SelectSlaveMode), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_SetAutoreload), (4 bytes).
    Removing stm32f10x_tim.o(i.TIM_SetClockDivision), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_SetIC1Prescaler), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_SetIC2Prescaler), (26 bytes).
    Removing stm32f10x_tim.o(i.TIM_SetIC3Prescaler), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_SetIC4Prescaler), (26 bytes).
    Removing stm32f10x_tim.o(i.TIM_TIxExternalClockConfig), (62 bytes).
    Removing stm32f10x_tim.o(i.TIM_TimeBaseStructInit), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_UpdateDisableConfig), (24 bytes).
    Removing stm32f10x_tim.o(i.TIM_UpdateRequestConfig), (24 bytes).
    Removing stm32f10x_usart.o(i.USART_ClearFlag), (18 bytes).
    Removing stm32f10x_usart.o(i.USART_ClearITPendingBit), (30 bytes).
    Removing stm32f10x_usart.o(i.USART_ClockInit), (34 bytes).
    Removing stm32f10x_usart.o(i.USART_ClockStructInit), (12 bytes).
    Removing stm32f10x_usart.o(i.USART_DMACmd), (18 bytes).
    Removing stm32f10x_usart.o(i.USART_DeInit), (156 bytes).
    Removing stm32f10x_usart.o(i.USART_GetFlagStatus), (26 bytes).
    Removing stm32f10x_usart.o(i.USART_GetITStatus), (84 bytes).
    Removing stm32f10x_usart.o(i.USART_HalfDuplexCmd), (24 bytes).
    Removing stm32f10x_usart.o(i.USART_ITConfig), (74 bytes).
    Removing stm32f10x_usart.o(i.USART_IrDACmd), (24 bytes).
    Removing stm32f10x_usart.o(i.USART_IrDAConfig), (18 bytes).
    Removing stm32f10x_usart.o(i.USART_LINBreakDetectLengthConfig), (18 bytes).
    Removing stm32f10x_usart.o(i.USART_LINCmd), (24 bytes).
    Removing stm32f10x_usart.o(i.USART_OneBitMethodCmd), (24 bytes).
    Removing stm32f10x_usart.o(i.USART_OverSampling8Cmd), (22 bytes).
    Removing stm32f10x_usart.o(i.USART_ReceiveData), (10 bytes).
    Removing stm32f10x_usart.o(i.USART_ReceiverWakeUpCmd), (24 bytes).
    Removing stm32f10x_usart.o(i.USART_SendBreak), (10 bytes).
    Removing stm32f10x_usart.o(i.USART_SendData), (8 bytes).
    Removing stm32f10x_usart.o(i.USART_SetAddress), (18 bytes).
    Removing stm32f10x_usart.o(i.USART_SetGuardTime), (16 bytes).
    Removing stm32f10x_usart.o(i.USART_SetPrescaler), (16 bytes).
    Removing stm32f10x_usart.o(i.USART_SmartCardCmd), (24 bytes).
    Removing stm32f10x_usart.o(i.USART_SmartCardNACKCmd), (24 bytes).
    Removing stm32f10x_usart.o(i.USART_StructInit), (24 bytes).
    Removing stm32f10x_usart.o(i.USART_WakeUpConfig), (18 bytes).
    Removing stm32f10x_wwdg.o(i.WWDG_ClearFlag), (12 bytes).
    Removing stm32f10x_wwdg.o(i.WWDG_DeInit), (22 bytes).
    Removing stm32f10x_wwdg.o(i.WWDG_Enable), (16 bytes).
    Removing stm32f10x_wwdg.o(i.WWDG_EnableIT), (12 bytes).
    Removing stm32f10x_wwdg.o(i.WWDG_GetFlagStatus), (12 bytes).
    Removing stm32f10x_wwdg.o(i.WWDG_SetCounter), (16 bytes).
    Removing stm32f10x_wwdg.o(i.WWDG_SetPrescaler), (24 bytes).
    Removing stm32f10x_wwdg.o(i.WWDG_SetWindowValue), (40 bytes).
    Removing led.o(i.LED_Init), (92 bytes).
    Removing led.o(i.LED_OFF), (36 bytes).
    Removing led.o(i.LED_ON), (36 bytes).
    Removing oled.o(i.OLED_Pow), (20 bytes).
    Removing oled.o(i.OLED_ShowBinNum), (62 bytes).
    Removing oled.o(i.OLED_ShowChar), (116 bytes).
    Removing oled.o(i.OLED_ShowHexNum), (84 bytes).
    Removing oled.o(i.OLED_ShowNum), (68 bytes).
    Removing oled.o(i.OLED_ShowSignedNum), (102 bytes).
    Removing oled.o(i.OLED_ShowString), (40 bytes).
    Removing oled.o(.constdata), (1520 bytes).
    Removing pca9685.o(i.calculate_PWM), (100 bytes).
    Removing pca9685.o(i.crazyMe), (144 bytes).
    Removing pca9685.o(i.setPWM), (58 bytes).
    Removing encoder.o(i.Reset_All_Encoders), (52 bytes).
    Removing pid_control.o(i.Motor_Control_Stop), (40 bytes).
    Removing pid_control.o(i.Motor_Control_Stop_All), (20 bytes).
    Removing pid_control.o(i.Position_Control_Start), (80 bytes).
    Removing pid_control.o(i.Reset_Accumulated_Position), (48 bytes).
    Removing sys.o(.emb_text), (16 bytes).
    Removing sys.o(i.Ex_NVIC_Config), (160 bytes).
    Removing sys.o(i.GPIO_AF_Set), (20 bytes).
    Removing sys.o(i.GPIO_Set), (408 bytes).
    Removing sys.o(i.MY_NVIC_Init), (120 bytes).
    Removing sys.o(i.MY_NVIC_PriorityGroupConfig), (40 bytes).
    Removing sys.o(i.MY_NVIC_SetVectorTable), (16 bytes).
    Removing sys.o(i.Stm32_Clock_Init), (152 bytes).
    Removing sys.o(i.Sys_Soft_Reset), (16 bytes).
    Removing sys.o(i.Sys_Standby), (76 bytes).
    Removing usart1.o(i.Usart1_Init), (120 bytes).
    Removing usart1.o(i.Usart1_ReceiveChar), (32 bytes).
    Removing usart1.o(i.Usart1_SendChar), (32 bytes).
    Removing usart1.o(i.Usart1_SendString), (22 bytes).
    Removing delay.o(i.delay_s), (24 bytes).
    Removing delay.o(i.delay_xms), (12 bytes).
    Removing usart3.o(i.USART3_ReceiveChar), (32 bytes).
    Removing usart3.o(i.USART3_SendChar), (32 bytes).
    Removing usart3.o(i.USART3_SendString), (22 bytes).
    Removing usart3.o(i.Usart3_Init), (128 bytes).
    Removing usart2.o(i.Usart2_ReceiveChar), (32 bytes).
    Removing usart2.o(i.Usart2_SendChar), (32 bytes).
    Removing usart2.o(i.Usart2_SendString), (22 bytes).
    Removing usart5.o(i.Usart5_ReceiveChar), (32 bytes).
    Removing usart5.o(i.Usart5_SendChar), (32 bytes).
    Removing usart5.o(i.Usart5_SendString), (22 bytes).
    Removing usart5.o(i.fputc), (28 bytes).
    Removing usart5.o(.data), (4 bytes).

490 unused section(s) (total 21668 bytes) removed from the image.

==============================================================================

Image Symbol Table

    Local Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardinit1.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardinit2.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardinit3.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardshut.o ABSOLUTE
    ../clib/angel/handlers.s                 0x00000000   Number         0  __scatter_zi.o ABSOLUTE
    ../clib/angel/handlers.s                 0x00000000   Number         0  __scatter_copy.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  __rtentry2.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  __rtentry.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  rtexit2.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  __rtentry4.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  rtexit.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_raise.o ABSOLUTE
    ../clib/angel/scatter.s                  0x00000000   Number         0  __scatter.o ABSOLUTE
    ../clib/angel/startup.s                  0x00000000   Number         0  __main.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  indicate_semi.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  sys_stackheap_outer.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  use_no_semi_2.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  use_no_semi.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  libspace.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_command.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_wrch.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  _get_argv_nomalloc.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  argv_veneer.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  argv_veneer.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  no_argv.o ABSOLUTE
    ../clib/heapalloc.c                      0x00000000   Number         0  hrguard.o ABSOLUTE
    ../clib/heapaux.c                        0x00000000   Number         0  heapauxi.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libshutdown2.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libinit.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libinit2.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libshutdown.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtred_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtmem_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_exit.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_general.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  __raise.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtmem_formal.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtmem_outer.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_abrt_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_fpe_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_stak_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_pvfn_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_segv_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_cppl_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_other.o ABSOLUTE
    ../clib/signal.s                         0x00000000   Number         0  defsig.o ABSOLUTE
    ../clib/stdlib.c                         0x00000000   Number         0  exit.o ABSOLUTE
    ../fplib/d2f.s                           0x00000000   Number         0  d2f.o ABSOLUTE
    ../fplib/daddsub.s                       0x00000000   Number         0  daddsub_clz.o ABSOLUTE
    ../fplib/dcmpi.s                         0x00000000   Number         0  dcmpi.o ABSOLUTE
    ../fplib/ddiv.s                          0x00000000   Number         0  ddiv.o ABSOLUTE
    ../fplib/dfixu.s                         0x00000000   Number         0  dfixu.o ABSOLUTE
    ../fplib/dflt.s                          0x00000000   Number         0  dflt_clz.o ABSOLUTE
    ../fplib/dleqf.s                         0x00000000   Number         0  dleqf.o ABSOLUTE
    ../fplib/dmul.s                          0x00000000   Number         0  dmul.o ABSOLUTE
    ../fplib/dnaninf.s                       0x00000000   Number         0  dnaninf.o ABSOLUTE
    ../fplib/dretinf.s                       0x00000000   Number         0  dretinf.o ABSOLUTE
    ../fplib/drleqf.s                        0x00000000   Number         0  drleqf.o ABSOLUTE
    ../fplib/f2d.s                           0x00000000   Number         0  f2d.o ABSOLUTE
    ../fplib/faddsub.s                       0x00000000   Number         0  faddsub_clz.o ABSOLUTE
    ../fplib/fcmpi.s                         0x00000000   Number         0  fcmpi.o ABSOLUTE
    ../fplib/fdiv.s                          0x00000000   Number         0  fdiv.o ABSOLUTE
    ../fplib/feqf.s                          0x00000000   Number         0  feqf.o ABSOLUTE
    ../fplib/ffix.s                          0x00000000   Number         0  ffix.o ABSOLUTE
    ../fplib/fflt.s                          0x00000000   Number         0  fflt_clz.o ABSOLUTE
    ../fplib/fleqf.s                         0x00000000   Number         0  fleqf.o ABSOLUTE
    ../fplib/fmul.s                          0x00000000   Number         0  fmul.o ABSOLUTE
    ../fplib/fnaninf.s                       0x00000000   Number         0  fnaninf.o ABSOLUTE
    ../fplib/fpinit.s                        0x00000000   Number         0  fpinit.o ABSOLUTE
    ../fplib/fretinf.s                       0x00000000   Number         0  fretinf.o ABSOLUTE
    ../fplib/frleqf.s                        0x00000000   Number         0  frleqf.o ABSOLUTE
    ../fplib/usenofp.s                       0x00000000   Number         0  usenofp.o ABSOLUTE
    ../mathlib/floor.c                       0x00000000   Number         0  floor.o ABSOLUTE
    Hardware\PCA9685.c                       0x00000000   Number         0  pca9685.o ABSOLUTE
    Hardware\TB6612.c                        0x00000000   Number         0  tb6612.o ABSOLUTE
    Hardware\encoder.c                       0x00000000   Number         0  encoder.o ABSOLUTE
    Hardware\led.c                           0x00000000   Number         0  led.o ABSOLUTE
    Hardware\oled.c                          0x00000000   Number         0  oled.o ABSOLUTE
    Hardware\pid_control.c                   0x00000000   Number         0  pid_control.o ABSOLUTE
    Library\misc.c                           0x00000000   Number         0  misc.o ABSOLUTE
    Library\stm32f10x_adc.c                  0x00000000   Number         0  stm32f10x_adc.o ABSOLUTE
    Library\stm32f10x_bkp.c                  0x00000000   Number         0  stm32f10x_bkp.o ABSOLUTE
    Library\stm32f10x_can.c                  0x00000000   Number         0  stm32f10x_can.o ABSOLUTE
    Library\stm32f10x_cec.c                  0x00000000   Number         0  stm32f10x_cec.o ABSOLUTE
    Library\stm32f10x_crc.c                  0x00000000   Number         0  stm32f10x_crc.o ABSOLUTE
    Library\stm32f10x_dac.c                  0x00000000   Number         0  stm32f10x_dac.o ABSOLUTE
    Library\stm32f10x_dbgmcu.c               0x00000000   Number         0  stm32f10x_dbgmcu.o ABSOLUTE
    Library\stm32f10x_dma.c                  0x00000000   Number         0  stm32f10x_dma.o ABSOLUTE
    Library\stm32f10x_exti.c                 0x00000000   Number         0  stm32f10x_exti.o ABSOLUTE
    Library\stm32f10x_flash.c                0x00000000   Number         0  stm32f10x_flash.o ABSOLUTE
    Library\stm32f10x_fsmc.c                 0x00000000   Number         0  stm32f10x_fsmc.o ABSOLUTE
    Library\stm32f10x_gpio.c                 0x00000000   Number         0  stm32f10x_gpio.o ABSOLUTE
    Library\stm32f10x_i2c.c                  0x00000000   Number         0  stm32f10x_i2c.o ABSOLUTE
    Library\stm32f10x_iwdg.c                 0x00000000   Number         0  stm32f10x_iwdg.o ABSOLUTE
    Library\stm32f10x_pwr.c                  0x00000000   Number         0  stm32f10x_pwr.o ABSOLUTE
    Library\stm32f10x_rcc.c                  0x00000000   Number         0  stm32f10x_rcc.o ABSOLUTE
    Library\stm32f10x_rtc.c                  0x00000000   Number         0  stm32f10x_rtc.o ABSOLUTE
    Library\stm32f10x_sdio.c                 0x00000000   Number         0  stm32f10x_sdio.o ABSOLUTE
    Library\stm32f10x_spi.c                  0x00000000   Number         0  stm32f10x_spi.o ABSOLUTE
    Library\stm32f10x_tim.c                  0x00000000   Number         0  stm32f10x_tim.o ABSOLUTE
    Library\stm32f10x_usart.c                0x00000000   Number         0  stm32f10x_usart.o ABSOLUTE
    Library\stm32f10x_wwdg.c                 0x00000000   Number         0  stm32f10x_wwdg.o ABSOLUTE
    Start\startup_stm32f10x_hd.s             0x00000000   Number         0  startup_stm32f10x_hd.o ABSOLUTE
    System\IIC.c                             0x00000000   Number         0  iic.o ABSOLUTE
    System\Usart1.c                          0x00000000   Number         0  usart1.o ABSOLUTE
    System\Usart2.c                          0x00000000   Number         0  usart2.o ABSOLUTE
    System\Usart3.c                          0x00000000   Number         0  usart3.o ABSOLUTE
    System\Usart5.c                          0x00000000   Number         0  usart5.o ABSOLUTE
    System\\sys.c                            0x00000000   Number         0  sys.o ABSOLUTE
    System\delay.c                           0x00000000   Number         0  delay.o ABSOLUTE
    System\sys.c                             0x00000000   Number         0  sys.o ABSOLUTE
    User\main.c                              0x00000000   Number         0  main.o ABSOLUTE
    dc.s                                     0x00000000   Number         0  dc.o ABSOLUTE
    start\\core_cm3.c                        0x00000000   Number         0  core_cm3.o ABSOLUTE
    start\core_cm3.c                         0x00000000   Number         0  core_cm3.o ABSOLUTE
    start\system_stm32f10x.c                 0x00000000   Number         0  system_stm32f10x.o ABSOLUTE
    RESET                                    0x08000000   Section      304  startup_stm32f10x_hd.o(RESET)
    !!!main                                  0x08000130   Section        8  __main.o(!!!main)
    !!!scatter                               0x08000138   Section       52  __scatter.o(!!!scatter)
    !!handler_copy                           0x0800016c   Section       26  __scatter_copy.o(!!handler_copy)
    !!handler_zi                             0x08000188   Section       28  __scatter_zi.o(!!handler_zi)
    .ARM.Collect$$libinit$$00000000          0x080001a4   Section        2  libinit.o(.ARM.Collect$$libinit$$00000000)
    .ARM.Collect$$libinit$$00000002          0x080001a6   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000002)
    .ARM.Collect$$libinit$$00000004          0x080001a6   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000004)
    .ARM.Collect$$libinit$$0000000A          0x080001a6   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000000A)
    .ARM.Collect$$libinit$$0000000C          0x080001a6   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000000C)
    .ARM.Collect$$libinit$$0000000E          0x080001a6   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000000E)
    .ARM.Collect$$libinit$$00000011          0x080001a6   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000011)
    .ARM.Collect$$libinit$$00000013          0x080001a6   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000013)
    .ARM.Collect$$libinit$$00000015          0x080001a6   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000015)
    .ARM.Collect$$libinit$$00000017          0x080001a6   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000017)
    .ARM.Collect$$libinit$$00000019          0x080001a6   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000019)
    .ARM.Collect$$libinit$$0000001B          0x080001a6   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000001B)
    .ARM.Collect$$libinit$$0000001D          0x080001a6   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000001D)
    .ARM.Collect$$libinit$$0000001F          0x080001a6   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000001F)
    .ARM.Collect$$libinit$$00000021          0x080001a6   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000021)
    .ARM.Collect$$libinit$$00000023          0x080001a6   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000023)
    .ARM.Collect$$libinit$$00000025          0x080001a6   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000025)
    .ARM.Collect$$libinit$$0000002C          0x080001a6   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000002C)
    .ARM.Collect$$libinit$$0000002E          0x080001a6   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000002E)
    .ARM.Collect$$libinit$$00000030          0x080001a6   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000030)
    .ARM.Collect$$libinit$$00000032          0x080001a6   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000032)
    .ARM.Collect$$libinit$$00000033          0x080001a6   Section        2  libinit2.o(.ARM.Collect$$libinit$$00000033)
    .ARM.Collect$$libshutdown$$00000000      0x080001a8   Section        2  libshutdown.o(.ARM.Collect$$libshutdown$$00000000)
    .ARM.Collect$$libshutdown$$00000002      0x080001aa   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000002)
    .ARM.Collect$$libshutdown$$00000004      0x080001aa   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000004)
    .ARM.Collect$$libshutdown$$00000006      0x080001aa   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000006)
    .ARM.Collect$$libshutdown$$00000009      0x080001aa   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000009)
    .ARM.Collect$$libshutdown$$0000000C      0x080001aa   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000C)
    .ARM.Collect$$libshutdown$$0000000E      0x080001aa   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000E)
    .ARM.Collect$$libshutdown$$00000011      0x080001aa   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000011)
    .ARM.Collect$$libshutdown$$00000012      0x080001aa   Section        2  libshutdown2.o(.ARM.Collect$$libshutdown$$00000012)
    .ARM.Collect$$rtentry$$00000000          0x080001ac   Section        0  __rtentry.o(.ARM.Collect$$rtentry$$00000000)
    .ARM.Collect$$rtentry$$00000002          0x080001ac   Section        0  __rtentry2.o(.ARM.Collect$$rtentry$$00000002)
    .ARM.Collect$$rtentry$$00000004          0x080001ac   Section        6  __rtentry4.o(.ARM.Collect$$rtentry$$00000004)
    .ARM.Collect$$rtentry$$00000009          0x080001b2   Section        0  __rtentry2.o(.ARM.Collect$$rtentry$$00000009)
    .ARM.Collect$$rtentry$$0000000A          0x080001b2   Section        4  __rtentry2.o(.ARM.Collect$$rtentry$$0000000A)
    .ARM.Collect$$rtentry$$0000000C          0x080001b6   Section        0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000C)
    .ARM.Collect$$rtentry$$0000000D          0x080001b6   Section        8  __rtentry2.o(.ARM.Collect$$rtentry$$0000000D)
    .ARM.Collect$$rtexit$$00000000           0x080001be   Section        2  rtexit.o(.ARM.Collect$$rtexit$$00000000)
    .ARM.Collect$$rtexit$$00000002           0x080001c0   Section        0  rtexit2.o(.ARM.Collect$$rtexit$$00000002)
    .ARM.Collect$$rtexit$$00000003           0x080001c0   Section        4  rtexit2.o(.ARM.Collect$$rtexit$$00000003)
    .ARM.Collect$$rtexit$$00000004           0x080001c4   Section        6  rtexit2.o(.ARM.Collect$$rtexit$$00000004)
    .text                                    0x080001cc   Section       64  startup_stm32f10x_hd.o(.text)
    .text                                    0x0800020c   Section        2  use_no_semi_2.o(.text)
    .text                                    0x0800020e   Section        0  heapauxi.o(.text)
    .text                                    0x08000214   Section        2  use_no_semi.o(.text)
    .text                                    0x08000216   Section       74  sys_stackheap_outer.o(.text)
    .text                                    0x08000260   Section        0  exit.o(.text)
    .text                                    0x08000274   Section        8  libspace.o(.text)
    i.Encoder1_Init                          0x0800027c   Section        0  encoder.o(i.Encoder1_Init)
    i.Encoder2_Init                          0x08000300   Section        0  encoder.o(i.Encoder2_Init)
    i.Encoder3_Init                          0x080003a0   Section        0  encoder.o(i.Encoder3_Init)
    i.Encoder4_Init                          0x0800041c   Section        0  encoder.o(i.Encoder4_Init)
    i.Encoder_Init                           0x08000498   Section        0  encoder.o(i.Encoder_Init)
    i.GPIO_Init                              0x080004ac   Section        0  stm32f10x_gpio.o(i.GPIO_Init)
    i.GPIO_PinRemapConfig                    0x080005c4   Section        0  stm32f10x_gpio.o(i.GPIO_PinRemapConfig)
    i.GPIO_ResetBits                         0x08000654   Section        0  stm32f10x_gpio.o(i.GPIO_ResetBits)
    i.GPIO_SetBits                           0x08000658   Section        0  stm32f10x_gpio.o(i.GPIO_SetBits)
    i.GPIO_WriteBit                          0x0800065c   Section        0  stm32f10x_gpio.o(i.GPIO_WriteBit)
    i.Get_Accumulated_Position               0x08000668   Section        0  pid_control.o(i.Get_Accumulated_Position)
    i.Get_Encoder_Count                      0x080006cc   Section        0  encoder.o(i.Get_Encoder_Count)
    i.Get_Motor_Speed                        0x0800071c   Section        0  pid_control.o(i.Get_Motor_Speed)
    i.Hardware_PWM_Init                      0x08000738   Section        0  tb6612.o(i.Hardware_PWM_Init)
    i.IIC_Ack                                0x08000844   Section        0  iic.o(i.IIC_Ack)
    i.IIC_Init                               0x08000894   Section        0  iic.o(i.IIC_Init)
    i.IIC_NAck                               0x080008d4   Section        0  iic.o(i.IIC_NAck)
    i.IIC_Read_Byte                          0x08000924   Section        0  iic.o(i.IIC_Read_Byte)
    i.IIC_Send_Byte                          0x08000994   Section        0  iic.o(i.IIC_Send_Byte)
    i.IIC_Start                              0x080009fc   Section        0  iic.o(i.IIC_Start)
    i.IIC_Stop                               0x08000a48   Section        0  iic.o(i.IIC_Stop)
    i.IIC_Wait_Ack                           0x08000a94   Section        0  iic.o(i.IIC_Wait_Ack)
    i.Limit_Value                            0x08000af8   Section        0  pid_control.o(i.Limit_Value)
    i.MOTOR_Init                             0x08000b24   Section        0  tb6612.o(i.MOTOR_Init)
    i.Motor_Direction_Init                   0x08000b34   Section        0  tb6612.o(i.Motor_Direction_Init)
    i.Motor_SetDirection                     0x08000ba0   Section        0  tb6612.o(i.Motor_SetDirection)
    i.Motor_SetPWM                           0x08000cc4   Section        0  tb6612.o(i.Motor_SetPWM)
    i.Move_stop                              0x08000d1c   Section        0  tb6612.o(i.Move_stop)
    i.NVIC_Init                              0x08000d60   Section        0  misc.o(i.NVIC_Init)
    i.OLED_Clear                             0x08000dd0   Section        0  oled.o(i.OLED_Clear)
    i.OLED_I2C_Init                          0x08000dfc   Section        0  oled.o(i.OLED_I2C_Init)
    i.OLED_I2C_SendByte                      0x08000e48   Section        0  oled.o(i.OLED_I2C_SendByte)
    i.OLED_I2C_Start                         0x08000ea0   Section        0  oled.o(i.OLED_I2C_Start)
    i.OLED_I2C_Stop                          0x08000ed0   Section        0  oled.o(i.OLED_I2C_Stop)
    i.OLED_Init                              0x08000ef8   Section        0  oled.o(i.OLED_Init)
    i.OLED_SetCursor                         0x08000fa6   Section        0  oled.o(i.OLED_SetCursor)
    i.OLED_WriteCommand                      0x08000fc8   Section        0  oled.o(i.OLED_WriteCommand)
    i.OLED_WriteData                         0x08000fe8   Section        0  oled.o(i.OLED_WriteData)
    i.PCA9685_Init                           0x08001008   Section        0  pca9685.o(i.PCA9685_Init)
    i.PCA9685_read                           0x08001024   Section        0  pca9685.o(i.PCA9685_read)
    i.PCA9685_write                          0x0800105e   Section        0  pca9685.o(i.PCA9685_write)
    i.PID_Control_Init                       0x0800108c   Section        0  pid_control.o(i.PID_Control_Init)
    i.Position_PID_RPM                       0x08001100   Section        0  pid_control.o(i.Position_PID_RPM)
    i.RCC_APB1PeriphClockCmd                 0x080011dc   Section        0  stm32f10x_rcc.o(i.RCC_APB1PeriphClockCmd)
    i.RCC_APB2PeriphClockCmd                 0x080011fc   Section        0  stm32f10x_rcc.o(i.RCC_APB2PeriphClockCmd)
    i.RCC_GetClocksFreq                      0x0800121c   Section        0  stm32f10x_rcc.o(i.RCC_GetClocksFreq)
    i.Reset_Encoder                          0x080012f0   Section        0  encoder.o(i.Reset_Encoder)
    i.Reset_Motor_State                      0x08001340   Section        0  pid_control.o(i.Reset_Motor_State)
    i.SetSysClock                            0x08001370   Section        0  system_stm32f10x.o(i.SetSysClock)
    SetSysClock                              0x08001371   Thumb Code     8  system_stm32f10x.o(i.SetSysClock)
    i.SetSysClockTo72                        0x08001378   Section        0  system_stm32f10x.o(i.SetSysClockTo72)
    SetSysClockTo72                          0x08001379   Thumb Code   214  system_stm32f10x.o(i.SetSysClockTo72)
    i.Set_Motor_PWM                          0x08001458   Section        0  tb6612.o(i.Set_Motor_PWM)
    i.Speed_Control_Start                    0x080014b8   Section        0  pid_control.o(i.Speed_Control_Start)
    i.Speed_PID_Calculate                    0x080014f4   Section        0  pid_control.o(i.Speed_PID_Calculate)
    i.SystemInit                             0x080015f8   Section        0  system_stm32f10x.o(i.SystemInit)
    i.TIM6_IRQHandler                        0x08001658   Section        0  pid_control.o(i.TIM6_IRQHandler)
    i.TIM_ClearITPendingBit                  0x08001790   Section        0  stm32f10x_tim.o(i.TIM_ClearITPendingBit)
    i.TIM_Cmd                                0x08001796   Section        0  stm32f10x_tim.o(i.TIM_Cmd)
    i.TIM_CtrlPWMOutputs                     0x080017ae   Section        0  stm32f10x_tim.o(i.TIM_CtrlPWMOutputs)
    i.TIM_EncoderInterfaceConfig             0x080017cc   Section        0  stm32f10x_tim.o(i.TIM_EncoderInterfaceConfig)
    i.TIM_GetCounter                         0x0800180e   Section        0  stm32f10x_tim.o(i.TIM_GetCounter)
    i.TIM_GetITStatus                        0x08001814   Section        0  stm32f10x_tim.o(i.TIM_GetITStatus)
    i.TIM_ITConfig                           0x08001836   Section        0  stm32f10x_tim.o(i.TIM_ITConfig)
    i.TIM_OC1Init                            0x08001848   Section        0  stm32f10x_tim.o(i.TIM_OC1Init)
    i.TIM_OC1PreloadConfig                   0x080018e0   Section        0  stm32f10x_tim.o(i.TIM_OC1PreloadConfig)
    i.TIM_OC2Init                            0x080018f4   Section        0  stm32f10x_tim.o(i.TIM_OC2Init)
    i.TIM_OC2PreloadConfig                   0x08001998   Section        0  stm32f10x_tim.o(i.TIM_OC2PreloadConfig)
    i.TIM_OC3Init                            0x080019b4   Section        0  stm32f10x_tim.o(i.TIM_OC3Init)
    i.TIM_OC3PreloadConfig                   0x08001a54   Section        0  stm32f10x_tim.o(i.TIM_OC3PreloadConfig)
    i.TIM_OC4Init                            0x08001a68   Section        0  stm32f10x_tim.o(i.TIM_OC4Init)
    i.TIM_OC4PreloadConfig                   0x08001ae4   Section        0  stm32f10x_tim.o(i.TIM_OC4PreloadConfig)
    i.TIM_SetCompare1                        0x08001afe   Section        0  stm32f10x_tim.o(i.TIM_SetCompare1)
    i.TIM_SetCompare2                        0x08001b02   Section        0  stm32f10x_tim.o(i.TIM_SetCompare2)
    i.TIM_SetCompare3                        0x08001b06   Section        0  stm32f10x_tim.o(i.TIM_SetCompare3)
    i.TIM_SetCompare4                        0x08001b0a   Section        0  stm32f10x_tim.o(i.TIM_SetCompare4)
    i.TIM_SetCounter                         0x08001b10   Section        0  stm32f10x_tim.o(i.TIM_SetCounter)
    i.TIM_TimeBaseInit                       0x08001b14   Section        0  stm32f10x_tim.o(i.TIM_TimeBaseInit)
    i.USART_Cmd                              0x08001bb8   Section        0  stm32f10x_usart.o(i.USART_Cmd)
    i.USART_Init                             0x08001bd0   Section        0  stm32f10x_usart.o(i.USART_Init)
    i.Usart2_Init                            0x08001ca8   Section        0  usart2.o(i.Usart2_Init)
    i.Usart5_Init                            0x08001d24   Section        0  usart5.o(i.Usart5_Init)
    i._sys_exit                              0x08001da4   Section        0  usart5.o(i._sys_exit)
    i.delay_init                             0x08001da8   Section        0  delay.o(i.delay_init)
    i.delay_ms                               0x08001dd8   Section        0  delay.o(i.delay_ms)
    i.delay_us                               0x08001df0   Section        0  delay.o(i.delay_us)
    i.floor                                  0x08001e2c   Section        0  floor.o(i.floor)
    i.init                                   0x08001f08   Section        0  main.o(i.init)
    i.main                                   0x08001f38   Section        0  main.o(i.main)
    i.setPWMFreq                             0x08001f64   Section        0  pca9685.o(i.setPWMFreq)
    x$fpl$dadd                               0x08001ffc   Section      336  daddsub_clz.o(x$fpl$dadd)
    _dadd1                                   0x0800200d   Thumb Code     0  daddsub_clz.o(x$fpl$dadd)
    x$fpl$dcmpinf                            0x0800214c   Section       24  dcmpi.o(x$fpl$dcmpinf)
    x$fpl$ddiv                               0x08002164   Section      688  ddiv.o(x$fpl$ddiv)
    ddiv_entry                               0x0800216b   Thumb Code     0  ddiv.o(x$fpl$ddiv)
    x$fpl$dfixu                              0x08002414   Section       90  dfixu.o(x$fpl$dfixu)
    x$fpl$dflt                               0x0800246e   Section       46  dflt_clz.o(x$fpl$dflt)
    x$fpl$dleqf                              0x0800249c   Section      120  dleqf.o(x$fpl$dleqf)
    x$fpl$dmul                               0x08002514   Section      340  dmul.o(x$fpl$dmul)
    x$fpl$dnaninf                            0x08002668   Section      156  dnaninf.o(x$fpl$dnaninf)
    x$fpl$dretinf                            0x08002704   Section       12  dretinf.o(x$fpl$dretinf)
    x$fpl$drleqf                             0x08002710   Section      108  drleqf.o(x$fpl$drleqf)
    x$fpl$drsb                               0x0800277c   Section       22  daddsub_clz.o(x$fpl$drsb)
    x$fpl$dsub                               0x08002794   Section      468  daddsub_clz.o(x$fpl$dsub)
    _dsub1                                   0x080027a5   Thumb Code     0  daddsub_clz.o(x$fpl$dsub)
    x$fpl$fadd                               0x08002968   Section      196  faddsub_clz.o(x$fpl$fadd)
    _fadd1                                   0x08002977   Thumb Code     0  faddsub_clz.o(x$fpl$fadd)
    x$fpl$fcmpinf                            0x08002a2c   Section       24  fcmpi.o(x$fpl$fcmpinf)
    x$fpl$fdiv                               0x08002a44   Section      388  fdiv.o(x$fpl$fdiv)
    _fdiv1                                   0x08002a45   Thumb Code     0  fdiv.o(x$fpl$fdiv)
    x$fpl$feqf                               0x08002bc8   Section      104  feqf.o(x$fpl$feqf)
    x$fpl$ffix                               0x08002c30   Section       54  ffix.o(x$fpl$ffix)
    x$fpl$fflt                               0x08002c68   Section       48  fflt_clz.o(x$fpl$fflt)
    x$fpl$fleqf                              0x08002c98   Section      104  fleqf.o(x$fpl$fleqf)
    x$fpl$fmul                               0x08002d00   Section      258  fmul.o(x$fpl$fmul)
    x$fpl$fnaninf                            0x08002e02   Section      140  fnaninf.o(x$fpl$fnaninf)
    x$fpl$fretinf                            0x08002e8e   Section       10  fretinf.o(x$fpl$fretinf)
    x$fpl$frleqf                             0x08002e98   Section       98  frleqf.o(x$fpl$frleqf)
    x$fpl$frsb                               0x08002efa   Section       20  faddsub_clz.o(x$fpl$frsb)
    x$fpl$fsub                               0x08002f10   Section      234  faddsub_clz.o(x$fpl$fsub)
    _fsub1                                   0x08002f1f   Thumb Code     0  faddsub_clz.o(x$fpl$fsub)
    x$fpl$usenofp                            0x08002ffa   Section        0  usenofp.o(x$fpl$usenofp)
    .data                                    0x20000000   Section       20  stm32f10x_rcc.o(.data)
    APBAHBPrescTable                         0x20000000   Data          16  stm32f10x_rcc.o(.data)
    ADCPrescTable                            0x20000010   Data           4  stm32f10x_rcc.o(.data)
    .data                                    0x20000014   Section       36  pid_control.o(.data)
    .data                                    0x20000038   Section        4  delay.o(.data)
    fac_us                                   0x20000038   Data           1  delay.o(.data)
    fac_ms                                   0x2000003a   Data           2  delay.o(.data)
    .bss                                     0x2000003c   Section      192  pid_control.o(.bss)
    motor_states                             0x2000003c   Data          96  pid_control.o(.bss)
    bias                                     0x2000009c   Data          16  pid_control.o(.bss)
    integral                                 0x200000ac   Data          16  pid_control.o(.bss)
    last_bias                                0x200000bc   Data          16  pid_control.o(.bss)
    last_error                               0x200000cc   Data          16  pid_control.o(.bss)
    prev_error                               0x200000dc   Data          16  pid_control.o(.bss)
    last_output                              0x200000ec   Data          16  pid_control.o(.bss)
    .bss                                     0x200000fc   Section       96  libspace.o(.bss)
    HEAP                                     0x20000160   Section      512  startup_stm32f10x_hd.o(HEAP)
    Heap_Mem                                 0x20000160   Data         512  startup_stm32f10x_hd.o(HEAP)
    STACK                                    0x20000360   Section     1024  startup_stm32f10x_hd.o(STACK)
    Stack_Mem                                0x20000360   Data        1024  startup_stm32f10x_hd.o(STACK)
    __initial_sp                             0x20000760   Data           0  startup_stm32f10x_hd.o(STACK)

    Global Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    BuildAttributes$$THM_ISAv4$P$D$K$B$S$PE$A:L22UL41UL21$X:L11$S22US41US21$IEEE1$IW$USESV6$~STKCKD$USESV7$~SHL$OTIME$ROPI$IEEEJ$EBA8$UX$STANDARDLIB$REQ8$PRES8$EABIv2 0x00000000   Number         0  anon$$obj.o ABSOLUTE
    __ARM_use_no_argv                        0x00000000   Number         0  main.o ABSOLUTE
    __ARM_exceptions_init                     - Undefined Weak Reference
    __alloca_initialize                       - Undefined Weak Reference
    __arm_fini_                               - Undefined Weak Reference
    __arm_preinit_                            - Undefined Weak Reference
    __cpp_initialize__aeabi_                  - Undefined Weak Reference
    __cxa_finalize                            - Undefined Weak Reference
    __rt_locale                               - Undefined Weak Reference
    __sigvec_lookup                           - Undefined Weak Reference
    _atexit_init                              - Undefined Weak Reference
    _call_atexit_fns                          - Undefined Weak Reference
    _clock_init                               - Undefined Weak Reference
    _fp_trap_init                             - Undefined Weak Reference
    _fp_trap_shutdown                         - Undefined Weak Reference
    _get_lc_collate                           - Undefined Weak Reference
    _get_lc_ctype                             - Undefined Weak Reference
    _get_lc_monetary                          - Undefined Weak Reference
    _get_lc_numeric                           - Undefined Weak Reference
    _get_lc_time                              - Undefined Weak Reference
    _getenv_init                              - Undefined Weak Reference
    _handle_redirection                       - Undefined Weak Reference
    _init_alloc                               - Undefined Weak Reference
    _init_user_alloc                          - Undefined Weak Reference
    _initio                                   - Undefined Weak Reference
    _rand_init                                - Undefined Weak Reference
    _signal_finish                            - Undefined Weak Reference
    _signal_init                              - Undefined Weak Reference
    _terminate_alloc                          - Undefined Weak Reference
    _terminate_user_alloc                     - Undefined Weak Reference
    _terminateio                              - Undefined Weak Reference
    __Vectors_Size                           0x00000130   Number         0  startup_stm32f10x_hd.o ABSOLUTE
    __Vectors                                0x08000000   Data           4  startup_stm32f10x_hd.o(RESET)
    __Vectors_End                            0x08000130   Data           0  startup_stm32f10x_hd.o(RESET)
    __main                                   0x08000131   Thumb Code     8  __main.o(!!!main)
    __scatterload                            0x08000139   Thumb Code     0  __scatter.o(!!!scatter)
    __scatterload_rt2                        0x08000139   Thumb Code    44  __scatter.o(!!!scatter)
    __scatterload_rt2_thumb_only             0x08000139   Thumb Code     0  __scatter.o(!!!scatter)
    __scatterload_null                       0x08000147   Thumb Code     0  __scatter.o(!!!scatter)
    __scatterload_copy                       0x0800016d   Thumb Code    26  __scatter_copy.o(!!handler_copy)
    __scatterload_zeroinit                   0x08000189   Thumb Code    28  __scatter_zi.o(!!handler_zi)
    __rt_lib_init                            0x080001a5   Thumb Code     0  libinit.o(.ARM.Collect$$libinit$$00000000)
    __rt_lib_init_alloca_1                   0x080001a7   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000002E)
    __rt_lib_init_argv_1                     0x080001a7   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000002C)
    __rt_lib_init_atexit_1                   0x080001a7   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000001B)
    __rt_lib_init_clock_1                    0x080001a7   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000021)
    __rt_lib_init_cpp_1                      0x080001a7   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000032)
    __rt_lib_init_exceptions_1               0x080001a7   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000030)
    __rt_lib_init_fp_1                       0x080001a7   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000002)
    __rt_lib_init_fp_trap_1                  0x080001a7   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000001F)
    __rt_lib_init_getenv_1                   0x080001a7   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000023)
    __rt_lib_init_heap_1                     0x080001a7   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000A)
    __rt_lib_init_lc_collate_1               0x080001a7   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000011)
    __rt_lib_init_lc_ctype_1                 0x080001a7   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000013)
    __rt_lib_init_lc_monetary_1              0x080001a7   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000015)
    __rt_lib_init_lc_numeric_1               0x080001a7   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000017)
    __rt_lib_init_lc_time_1                  0x080001a7   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000019)
    __rt_lib_init_preinit_1                  0x080001a7   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000004)
    __rt_lib_init_rand_1                     0x080001a7   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000E)
    __rt_lib_init_return                     0x080001a7   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000033)
    __rt_lib_init_signal_1                   0x080001a7   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000001D)
    __rt_lib_init_stdio_1                    0x080001a7   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000025)
    __rt_lib_init_user_alloc_1               0x080001a7   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000C)
    __rt_lib_shutdown                        0x080001a9   Thumb Code     0  libshutdown.o(.ARM.Collect$$libshutdown$$00000000)
    __rt_lib_shutdown_cpp_1                  0x080001ab   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000004)
    __rt_lib_shutdown_fini_1                 0x080001ab   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000002)
    __rt_lib_shutdown_fp_trap_1              0x080001ab   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000009)
    __rt_lib_shutdown_heap_1                 0x080001ab   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000011)
    __rt_lib_shutdown_return                 0x080001ab   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000012)
    __rt_lib_shutdown_signal_1               0x080001ab   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000C)
    __rt_lib_shutdown_stdio_1                0x080001ab   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000006)
    __rt_lib_shutdown_user_alloc_1           0x080001ab   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000E)
    __rt_entry                               0x080001ad   Thumb Code     0  __rtentry.o(.ARM.Collect$$rtentry$$00000000)
    __rt_entry_presh_1                       0x080001ad   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$00000002)
    __rt_entry_sh                            0x080001ad   Thumb Code     0  __rtentry4.o(.ARM.Collect$$rtentry$$00000004)
    __rt_entry_li                            0x080001b3   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000A)
    __rt_entry_postsh_1                      0x080001b3   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$00000009)
    __rt_entry_main                          0x080001b7   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000D)
    __rt_entry_postli_1                      0x080001b7   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000C)
    __rt_exit                                0x080001bf   Thumb Code     0  rtexit.o(.ARM.Collect$$rtexit$$00000000)
    __rt_exit_ls                             0x080001c1   Thumb Code     0  rtexit2.o(.ARM.Collect$$rtexit$$00000003)
    __rt_exit_prels_1                        0x080001c1   Thumb Code     0  rtexit2.o(.ARM.Collect$$rtexit$$00000002)
    __rt_exit_exit                           0x080001c5   Thumb Code     0  rtexit2.o(.ARM.Collect$$rtexit$$00000004)
    Reset_Handler                            0x080001cd   Thumb Code     8  startup_stm32f10x_hd.o(.text)
    NMI_Handler                              0x080001d5   Thumb Code     2  startup_stm32f10x_hd.o(.text)
    HardFault_Handler                        0x080001d7   Thumb Code     2  startup_stm32f10x_hd.o(.text)
    MemManage_Handler                        0x080001d9   Thumb Code     2  startup_stm32f10x_hd.o(.text)
    BusFault_Handler                         0x080001db   Thumb Code     2  startup_stm32f10x_hd.o(.text)
    UsageFault_Handler                       0x080001dd   Thumb Code     2  startup_stm32f10x_hd.o(.text)
    SVC_Handler                              0x080001df   Thumb Code     2  startup_stm32f10x_hd.o(.text)
    DebugMon_Handler                         0x080001e1   Thumb Code     2  startup_stm32f10x_hd.o(.text)
    PendSV_Handler                           0x080001e3   Thumb Code     2  startup_stm32f10x_hd.o(.text)
    SysTick_Handler                          0x080001e5   Thumb Code     2  startup_stm32f10x_hd.o(.text)
    ADC1_2_IRQHandler                        0x080001e7   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    ADC3_IRQHandler                          0x080001e7   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    CAN1_RX1_IRQHandler                      0x080001e7   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    CAN1_SCE_IRQHandler                      0x080001e7   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    DMA1_Channel1_IRQHandler                 0x080001e7   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    DMA1_Channel2_IRQHandler                 0x080001e7   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    DMA1_Channel3_IRQHandler                 0x080001e7   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    DMA1_Channel4_IRQHandler                 0x080001e7   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    DMA1_Channel5_IRQHandler                 0x080001e7   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    DMA1_Channel6_IRQHandler                 0x080001e7   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    DMA1_Channel7_IRQHandler                 0x080001e7   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    DMA2_Channel1_IRQHandler                 0x080001e7   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    DMA2_Channel2_IRQHandler                 0x080001e7   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    DMA2_Channel3_IRQHandler                 0x080001e7   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    DMA2_Channel4_5_IRQHandler               0x080001e7   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    EXTI0_IRQHandler                         0x080001e7   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    EXTI15_10_IRQHandler                     0x080001e7   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    EXTI1_IRQHandler                         0x080001e7   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    EXTI2_IRQHandler                         0x080001e7   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    EXTI3_IRQHandler                         0x080001e7   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    EXTI4_IRQHandler                         0x080001e7   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    EXTI9_5_IRQHandler                       0x080001e7   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    FLASH_IRQHandler                         0x080001e7   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    FSMC_IRQHandler                          0x080001e7   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    I2C1_ER_IRQHandler                       0x080001e7   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    I2C1_EV_IRQHandler                       0x080001e7   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    I2C2_ER_IRQHandler                       0x080001e7   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    I2C2_EV_IRQHandler                       0x080001e7   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    PVD_IRQHandler                           0x080001e7   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    RCC_IRQHandler                           0x080001e7   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    RTCAlarm_IRQHandler                      0x080001e7   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    RTC_IRQHandler                           0x080001e7   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    SDIO_IRQHandler                          0x080001e7   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    SPI1_IRQHandler                          0x080001e7   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    SPI2_IRQHandler                          0x080001e7   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    SPI3_IRQHandler                          0x080001e7   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TAMPER_IRQHandler                        0x080001e7   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM1_BRK_IRQHandler                      0x080001e7   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM1_CC_IRQHandler                       0x080001e7   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM1_TRG_COM_IRQHandler                  0x080001e7   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM1_UP_IRQHandler                       0x080001e7   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM2_IRQHandler                          0x080001e7   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM3_IRQHandler                          0x080001e7   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM4_IRQHandler                          0x080001e7   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM5_IRQHandler                          0x080001e7   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM7_IRQHandler                          0x080001e7   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM8_BRK_IRQHandler                      0x080001e7   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM8_CC_IRQHandler                       0x080001e7   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM8_TRG_COM_IRQHandler                  0x080001e7   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM8_UP_IRQHandler                       0x080001e7   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    UART4_IRQHandler                         0x080001e7   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    UART5_IRQHandler                         0x080001e7   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    USART1_IRQHandler                        0x080001e7   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    USART2_IRQHandler                        0x080001e7   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    USART3_IRQHandler                        0x080001e7   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    USBWakeUp_IRQHandler                     0x080001e7   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    USB_HP_CAN1_TX_IRQHandler                0x080001e7   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    USB_LP_CAN1_RX0_IRQHandler               0x080001e7   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    WWDG_IRQHandler                          0x080001e7   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    __user_initial_stackheap                 0x080001e9   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    __use_no_semihosting                     0x0800020d   Thumb Code     2  use_no_semi_2.o(.text)
    __use_two_region_memory                  0x0800020f   Thumb Code     2  heapauxi.o(.text)
    __rt_heap_escrow$2region                 0x08000211   Thumb Code     2  heapauxi.o(.text)
    __rt_heap_expand$2region                 0x08000213   Thumb Code     2  heapauxi.o(.text)
    __I$use$semihosting                      0x08000215   Thumb Code     0  use_no_semi.o(.text)
    __use_no_semihosting_swi                 0x08000215   Thumb Code     2  use_no_semi.o(.text)
    __user_setup_stackheap                   0x08000217   Thumb Code    74  sys_stackheap_outer.o(.text)
    exit                                     0x08000261   Thumb Code    18  exit.o(.text)
    __user_libspace                          0x08000275   Thumb Code     8  libspace.o(.text)
    __user_perproc_libspace                  0x08000275   Thumb Code     0  libspace.o(.text)
    __user_perthread_libspace                0x08000275   Thumb Code     0  libspace.o(.text)
    Encoder1_Init                            0x0800027d   Thumb Code   120  encoder.o(i.Encoder1_Init)
    Encoder2_Init                            0x08000301   Thumb Code   142  encoder.o(i.Encoder2_Init)
    Encoder3_Init                            0x080003a1   Thumb Code   112  encoder.o(i.Encoder3_Init)
    Encoder4_Init                            0x0800041d   Thumb Code   114  encoder.o(i.Encoder4_Init)
    Encoder_Init                             0x08000499   Thumb Code    20  encoder.o(i.Encoder_Init)
    GPIO_Init                                0x080004ad   Thumb Code   278  stm32f10x_gpio.o(i.GPIO_Init)
    GPIO_PinRemapConfig                      0x080005c5   Thumb Code   138  stm32f10x_gpio.o(i.GPIO_PinRemapConfig)
    GPIO_ResetBits                           0x08000655   Thumb Code     4  stm32f10x_gpio.o(i.GPIO_ResetBits)
    GPIO_SetBits                             0x08000659   Thumb Code     4  stm32f10x_gpio.o(i.GPIO_SetBits)
    GPIO_WriteBit                            0x0800065d   Thumb Code    10  stm32f10x_gpio.o(i.GPIO_WriteBit)
    Get_Accumulated_Position                 0x08000669   Thumb Code    82  pid_control.o(i.Get_Accumulated_Position)
    Get_Encoder_Count                        0x080006cd   Thumb Code    66  encoder.o(i.Get_Encoder_Count)
    Get_Motor_Speed                          0x0800071d   Thumb Code    24  pid_control.o(i.Get_Motor_Speed)
    Hardware_PWM_Init                        0x08000739   Thumb Code   250  tb6612.o(i.Hardware_PWM_Init)
    IIC_Ack                                  0x08000845   Thumb Code    66  iic.o(i.IIC_Ack)
    IIC_Init                                 0x08000895   Thumb Code    52  iic.o(i.IIC_Init)
    IIC_NAck                                 0x080008d5   Thumb Code    66  iic.o(i.IIC_NAck)
    IIC_Read_Byte                            0x08000925   Thumb Code    98  iic.o(i.IIC_Read_Byte)
    IIC_Send_Byte                            0x08000995   Thumb Code    90  iic.o(i.IIC_Send_Byte)
    IIC_Start                                0x080009fd   Thumb Code    64  iic.o(i.IIC_Start)
    IIC_Stop                                 0x08000a49   Thumb Code    62  iic.o(i.IIC_Stop)
    IIC_Wait_Ack                             0x08000a95   Thumb Code    86  iic.o(i.IIC_Wait_Ack)
    Limit_Value                              0x08000af9   Thumb Code    44  pid_control.o(i.Limit_Value)
    MOTOR_Init                               0x08000b25   Thumb Code    16  tb6612.o(i.MOTOR_Init)
    Motor_Direction_Init                     0x08000b35   Thumb Code    94  tb6612.o(i.Motor_Direction_Init)
    Motor_SetDirection                       0x08000ba1   Thumb Code   280  tb6612.o(i.Motor_SetDirection)
    Motor_SetPWM                             0x08000cc5   Thumb Code    80  tb6612.o(i.Motor_SetPWM)
    Move_stop                                0x08000d1d   Thumb Code    68  tb6612.o(i.Move_stop)
    NVIC_Init                                0x08000d61   Thumb Code   100  misc.o(i.NVIC_Init)
    OLED_Clear                               0x08000dd1   Thumb Code    42  oled.o(i.OLED_Clear)
    OLED_I2C_Init                            0x08000dfd   Thumb Code    72  oled.o(i.OLED_I2C_Init)
    OLED_I2C_SendByte                        0x08000e49   Thumb Code    82  oled.o(i.OLED_I2C_SendByte)
    OLED_I2C_Start                           0x08000ea1   Thumb Code    44  oled.o(i.OLED_I2C_Start)
    OLED_I2C_Stop                            0x08000ed1   Thumb Code    34  oled.o(i.OLED_I2C_Stop)
    OLED_Init                                0x08000ef9   Thumb Code   174  oled.o(i.OLED_Init)
    OLED_SetCursor                           0x08000fa7   Thumb Code    34  oled.o(i.OLED_SetCursor)
    OLED_WriteCommand                        0x08000fc9   Thumb Code    32  oled.o(i.OLED_WriteCommand)
    OLED_WriteData                           0x08000fe9   Thumb Code    32  oled.o(i.OLED_WriteData)
    PCA9685_Init                             0x08001009   Thumb Code    28  pca9685.o(i.PCA9685_Init)
    PCA9685_read                             0x08001025   Thumb Code    58  pca9685.o(i.PCA9685_read)
    PCA9685_write                            0x0800105f   Thumb Code    46  pca9685.o(i.PCA9685_write)
    PID_Control_Init                         0x0800108d   Thumb Code   112  pid_control.o(i.PID_Control_Init)
    Position_PID_RPM                         0x08001101   Thumb Code   182  pid_control.o(i.Position_PID_RPM)
    RCC_APB1PeriphClockCmd                   0x080011dd   Thumb Code    26  stm32f10x_rcc.o(i.RCC_APB1PeriphClockCmd)
    RCC_APB2PeriphClockCmd                   0x080011fd   Thumb Code    26  stm32f10x_rcc.o(i.RCC_APB2PeriphClockCmd)
    RCC_GetClocksFreq                        0x0800121d   Thumb Code   192  stm32f10x_rcc.o(i.RCC_GetClocksFreq)
    Reset_Encoder                            0x080012f1   Thumb Code    68  encoder.o(i.Reset_Encoder)
    Reset_Motor_State                        0x08001341   Thumb Code    44  pid_control.o(i.Reset_Motor_State)
    Set_Motor_PWM                            0x08001459   Thumb Code    96  tb6612.o(i.Set_Motor_PWM)
    Speed_Control_Start                      0x080014b9   Thumb Code    54  pid_control.o(i.Speed_Control_Start)
    Speed_PID_Calculate                      0x080014f5   Thumb Code   244  pid_control.o(i.Speed_PID_Calculate)
    SystemInit                               0x080015f9   Thumb Code    78  system_stm32f10x.o(i.SystemInit)
    TIM6_IRQHandler                          0x08001659   Thumb Code   278  pid_control.o(i.TIM6_IRQHandler)
    TIM_ClearITPendingBit                    0x08001791   Thumb Code     6  stm32f10x_tim.o(i.TIM_ClearITPendingBit)
    TIM_Cmd                                  0x08001797   Thumb Code    24  stm32f10x_tim.o(i.TIM_Cmd)
    TIM_CtrlPWMOutputs                       0x080017af   Thumb Code    30  stm32f10x_tim.o(i.TIM_CtrlPWMOutputs)
    TIM_EncoderInterfaceConfig               0x080017cd   Thumb Code    66  stm32f10x_tim.o(i.TIM_EncoderInterfaceConfig)
    TIM_GetCounter                           0x0800180f   Thumb Code     6  stm32f10x_tim.o(i.TIM_GetCounter)
    TIM_GetITStatus                          0x08001815   Thumb Code    34  stm32f10x_tim.o(i.TIM_GetITStatus)
    TIM_ITConfig                             0x08001837   Thumb Code    18  stm32f10x_tim.o(i.TIM_ITConfig)
    TIM_OC1Init                              0x08001849   Thumb Code   132  stm32f10x_tim.o(i.TIM_OC1Init)
    TIM_OC1PreloadConfig                     0x080018e1   Thumb Code    18  stm32f10x_tim.o(i.TIM_OC1PreloadConfig)
    TIM_OC2Init                              0x080018f5   Thumb Code   154  stm32f10x_tim.o(i.TIM_OC2Init)
    TIM_OC2PreloadConfig                     0x08001999   Thumb Code    26  stm32f10x_tim.o(i.TIM_OC2PreloadConfig)
    TIM_OC3Init                              0x080019b5   Thumb Code   150  stm32f10x_tim.o(i.TIM_OC3Init)
    TIM_OC3PreloadConfig                     0x08001a55   Thumb Code    18  stm32f10x_tim.o(i.TIM_OC3PreloadConfig)
    TIM_OC4Init                              0x08001a69   Thumb Code   114  stm32f10x_tim.o(i.TIM_OC4Init)
    TIM_OC4PreloadConfig                     0x08001ae5   Thumb Code    26  stm32f10x_tim.o(i.TIM_OC4PreloadConfig)
    TIM_SetCompare1                          0x08001aff   Thumb Code     4  stm32f10x_tim.o(i.TIM_SetCompare1)
    TIM_SetCompare2                          0x08001b03   Thumb Code     4  stm32f10x_tim.o(i.TIM_SetCompare2)
    TIM_SetCompare3                          0x08001b07   Thumb Code     4  stm32f10x_tim.o(i.TIM_SetCompare3)
    TIM_SetCompare4                          0x08001b0b   Thumb Code     6  stm32f10x_tim.o(i.TIM_SetCompare4)
    TIM_SetCounter                           0x08001b11   Thumb Code     4  stm32f10x_tim.o(i.TIM_SetCounter)
    TIM_TimeBaseInit                         0x08001b15   Thumb Code   122  stm32f10x_tim.o(i.TIM_TimeBaseInit)
    USART_Cmd                                0x08001bb9   Thumb Code    24  stm32f10x_usart.o(i.USART_Cmd)
    USART_Init                               0x08001bd1   Thumb Code   210  stm32f10x_usart.o(i.USART_Init)
    Usart2_Init                              0x08001ca9   Thumb Code   114  usart2.o(i.Usart2_Init)
    Usart5_Init                              0x08001d25   Thumb Code   114  usart5.o(i.Usart5_Init)
    _sys_exit                                0x08001da5   Thumb Code     4  usart5.o(i._sys_exit)
    delay_init                               0x08001da9   Thumb Code    40  delay.o(i.delay_init)
    delay_ms                                 0x08001dd9   Thumb Code    24  delay.o(i.delay_ms)
    delay_us                                 0x08001df1   Thumb Code    56  delay.o(i.delay_us)
    floor                                    0x08001e2d   Thumb Code   204  floor.o(i.floor)
    init                                     0x08001f09   Thumb Code    46  main.o(i.init)
    main                                     0x08001f39   Thumb Code    40  main.o(i.main)
    setPWMFreq                               0x08001f65   Thumb Code   134  pca9685.o(i.setPWMFreq)
    __aeabi_dadd                             0x08001ffd   Thumb Code     0  daddsub_clz.o(x$fpl$dadd)
    _dadd                                    0x08001ffd   Thumb Code   332  daddsub_clz.o(x$fpl$dadd)
    __fpl_dcmp_Inf                           0x0800214d   Thumb Code    24  dcmpi.o(x$fpl$dcmpinf)
    __aeabi_ddiv                             0x08002165   Thumb Code     0  ddiv.o(x$fpl$ddiv)
    _ddiv                                    0x08002165   Thumb Code   552  ddiv.o(x$fpl$ddiv)
    __aeabi_d2uiz                            0x08002415   Thumb Code     0  dfixu.o(x$fpl$dfixu)
    _dfixu                                   0x08002415   Thumb Code    90  dfixu.o(x$fpl$dfixu)
    __aeabi_i2d                              0x0800246f   Thumb Code     0  dflt_clz.o(x$fpl$dflt)
    _dflt                                    0x0800246f   Thumb Code    46  dflt_clz.o(x$fpl$dflt)
    __aeabi_cdcmple                          0x0800249d   Thumb Code     0  dleqf.o(x$fpl$dleqf)
    _dcmple                                  0x0800249d   Thumb Code   120  dleqf.o(x$fpl$dleqf)
    __fpl_dcmple_InfNaN                      0x080024ff   Thumb Code     0  dleqf.o(x$fpl$dleqf)
    __aeabi_dmul                             0x08002515   Thumb Code     0  dmul.o(x$fpl$dmul)
    _dmul                                    0x08002515   Thumb Code   332  dmul.o(x$fpl$dmul)
    __fpl_dnaninf                            0x08002669   Thumb Code   156  dnaninf.o(x$fpl$dnaninf)
    __fpl_dretinf                            0x08002705   Thumb Code    12  dretinf.o(x$fpl$dretinf)
    __aeabi_cdrcmple                         0x08002711   Thumb Code     0  drleqf.o(x$fpl$drleqf)
    _drcmple                                 0x08002711   Thumb Code   108  drleqf.o(x$fpl$drleqf)
    __aeabi_drsub                            0x0800277d   Thumb Code     0  daddsub_clz.o(x$fpl$drsb)
    _drsb                                    0x0800277d   Thumb Code    22  daddsub_clz.o(x$fpl$drsb)
    __aeabi_dsub                             0x08002795   Thumb Code     0  daddsub_clz.o(x$fpl$dsub)
    _dsub                                    0x08002795   Thumb Code   464  daddsub_clz.o(x$fpl$dsub)
    __aeabi_fadd                             0x08002969   Thumb Code     0  faddsub_clz.o(x$fpl$fadd)
    _fadd                                    0x08002969   Thumb Code   196  faddsub_clz.o(x$fpl$fadd)
    __fpl_fcmp_Inf                           0x08002a2d   Thumb Code    24  fcmpi.o(x$fpl$fcmpinf)
    __aeabi_fdiv                             0x08002a45   Thumb Code     0  fdiv.o(x$fpl$fdiv)
    _fdiv                                    0x08002a45   Thumb Code   384  fdiv.o(x$fpl$fdiv)
    __aeabi_cfcmpeq                          0x08002bc9   Thumb Code     0  feqf.o(x$fpl$feqf)
    _fcmpeq                                  0x08002bc9   Thumb Code   104  feqf.o(x$fpl$feqf)
    __aeabi_f2iz                             0x08002c31   Thumb Code     0  ffix.o(x$fpl$ffix)
    _ffix                                    0x08002c31   Thumb Code    54  ffix.o(x$fpl$ffix)
    __aeabi_i2f                              0x08002c69   Thumb Code     0  fflt_clz.o(x$fpl$fflt)
    _fflt                                    0x08002c69   Thumb Code    48  fflt_clz.o(x$fpl$fflt)
    __aeabi_cfcmple                          0x08002c99   Thumb Code     0  fleqf.o(x$fpl$fleqf)
    _fcmple                                  0x08002c99   Thumb Code   104  fleqf.o(x$fpl$fleqf)
    __fpl_fcmple_InfNaN                      0x08002ceb   Thumb Code     0  fleqf.o(x$fpl$fleqf)
    __aeabi_fmul                             0x08002d01   Thumb Code     0  fmul.o(x$fpl$fmul)
    _fmul                                    0x08002d01   Thumb Code   258  fmul.o(x$fpl$fmul)
    __fpl_fnaninf                            0x08002e03   Thumb Code   140  fnaninf.o(x$fpl$fnaninf)
    __fpl_fretinf                            0x08002e8f   Thumb Code    10  fretinf.o(x$fpl$fretinf)
    __aeabi_cfrcmple                         0x08002e99   Thumb Code     0  frleqf.o(x$fpl$frleqf)
    _frcmple                                 0x08002e99   Thumb Code    98  frleqf.o(x$fpl$frleqf)
    __aeabi_frsub                            0x08002efb   Thumb Code     0  faddsub_clz.o(x$fpl$frsb)
    _frsb                                    0x08002efb   Thumb Code    20  faddsub_clz.o(x$fpl$frsb)
    __aeabi_fsub                             0x08002f11   Thumb Code     0  faddsub_clz.o(x$fpl$fsub)
    _fsub                                    0x08002f11   Thumb Code   234  faddsub_clz.o(x$fpl$fsub)
    __I$use$fp                               0x08002ffa   Number         0  usenofp.o(x$fpl$usenofp)
    Region$$Table$$Base                      0x08002ffc   Number         0  anon$$obj.o(Region$$Table)
    Region$$Table$$Limit                     0x0800301c   Number         0  anon$$obj.o(Region$$Table)
    Position_KP                              0x20000014   Data           4  pid_control.o(.data)
    Position_KI                              0x20000018   Data           4  pid_control.o(.data)
    Position_KD                              0x2000001c   Data           4  pid_control.o(.data)
    Cascade_Speed_KP                         0x20000020   Data           4  pid_control.o(.data)
    Cascade_Speed_KI                         0x20000024   Data           4  pid_control.o(.data)
    Cascade_Speed_KD                         0x20000028   Data           4  pid_control.o(.data)
    Direct_Speed_KP                          0x2000002c   Data           4  pid_control.o(.data)
    Direct_Speed_KI                          0x20000030   Data           4  pid_control.o(.data)
    Direct_Speed_KD                          0x20000034   Data           4  pid_control.o(.data)
    __libspace_start                         0x200000fc   Data          96  libspace.o(.bss)
    __temporary_stack_top$libspace           0x2000015c   Data           0  libspace.o(.bss)



==============================================================================

Memory Map of the image

  Image Entry point : 0x08000131

  Load Region LR_IROM1 (Base: 0x08000000, Size: 0x00003058, Max: 0x00080000, ABSOLUTE)

    Execution Region ER_IROM1 (Exec base: 0x08000000, Load base: 0x08000000, Size: 0x0000301c, Max: 0x00080000, ABSOLUTE)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x08000000   0x08000000   0x00000130   Data   RO          132    RESET               startup_stm32f10x_hd.o
    0x08000130   0x08000130   0x00000008   Code   RO         3974  * !!!main             c_w.l(__main.o)
    0x08000138   0x08000138   0x00000034   Code   RO         4206    !!!scatter          c_w.l(__scatter.o)
    0x0800016c   0x0800016c   0x0000001a   Code   RO         4208    !!handler_copy      c_w.l(__scatter_copy.o)
    0x08000186   0x08000186   0x00000002   PAD
    0x08000188   0x08000188   0x0000001c   Code   RO         4210    !!handler_zi        c_w.l(__scatter_zi.o)
    0x080001a4   0x080001a4   0x00000002   Code   RO         4076    .ARM.Collect$$libinit$$00000000  c_w.l(libinit.o)
    0x080001a6   0x080001a6   0x00000000   Code   RO         4085    .ARM.Collect$$libinit$$00000002  c_w.l(libinit2.o)
    0x080001a6   0x080001a6   0x00000000   Code   RO         4087    .ARM.Collect$$libinit$$00000004  c_w.l(libinit2.o)
    0x080001a6   0x080001a6   0x00000000   Code   RO         4090    .ARM.Collect$$libinit$$0000000A  c_w.l(libinit2.o)
    0x080001a6   0x080001a6   0x00000000   Code   RO         4092    .ARM.Collect$$libinit$$0000000C  c_w.l(libinit2.o)
    0x080001a6   0x080001a6   0x00000000   Code   RO         4094    .ARM.Collect$$libinit$$0000000E  c_w.l(libinit2.o)
    0x080001a6   0x080001a6   0x00000000   Code   RO         4097    .ARM.Collect$$libinit$$00000011  c_w.l(libinit2.o)
    0x080001a6   0x080001a6   0x00000000   Code   RO         4099    .ARM.Collect$$libinit$$00000013  c_w.l(libinit2.o)
    0x080001a6   0x080001a6   0x00000000   Code   RO         4101    .ARM.Collect$$libinit$$00000015  c_w.l(libinit2.o)
    0x080001a6   0x080001a6   0x00000000   Code   RO         4103    .ARM.Collect$$libinit$$00000017  c_w.l(libinit2.o)
    0x080001a6   0x080001a6   0x00000000   Code   RO         4105    .ARM.Collect$$libinit$$00000019  c_w.l(libinit2.o)
    0x080001a6   0x080001a6   0x00000000   Code   RO         4107    .ARM.Collect$$libinit$$0000001B  c_w.l(libinit2.o)
    0x080001a6   0x080001a6   0x00000000   Code   RO         4109    .ARM.Collect$$libinit$$0000001D  c_w.l(libinit2.o)
    0x080001a6   0x080001a6   0x00000000   Code   RO         4111    .ARM.Collect$$libinit$$0000001F  c_w.l(libinit2.o)
    0x080001a6   0x080001a6   0x00000000   Code   RO         4113    .ARM.Collect$$libinit$$00000021  c_w.l(libinit2.o)
    0x080001a6   0x080001a6   0x00000000   Code   RO         4115    .ARM.Collect$$libinit$$00000023  c_w.l(libinit2.o)
    0x080001a6   0x080001a6   0x00000000   Code   RO         4117    .ARM.Collect$$libinit$$00000025  c_w.l(libinit2.o)
    0x080001a6   0x080001a6   0x00000000   Code   RO         4121    .ARM.Collect$$libinit$$0000002C  c_w.l(libinit2.o)
    0x080001a6   0x080001a6   0x00000000   Code   RO         4123    .ARM.Collect$$libinit$$0000002E  c_w.l(libinit2.o)
    0x080001a6   0x080001a6   0x00000000   Code   RO         4125    .ARM.Collect$$libinit$$00000030  c_w.l(libinit2.o)
    0x080001a6   0x080001a6   0x00000000   Code   RO         4127    .ARM.Collect$$libinit$$00000032  c_w.l(libinit2.o)
    0x080001a6   0x080001a6   0x00000002   Code   RO         4128    .ARM.Collect$$libinit$$00000033  c_w.l(libinit2.o)
    0x080001a8   0x080001a8   0x00000002   Code   RO         4146    .ARM.Collect$$libshutdown$$00000000  c_w.l(libshutdown.o)
    0x080001aa   0x080001aa   0x00000000   Code   RO         4156    .ARM.Collect$$libshutdown$$00000002  c_w.l(libshutdown2.o)
    0x080001aa   0x080001aa   0x00000000   Code   RO         4158    .ARM.Collect$$libshutdown$$00000004  c_w.l(libshutdown2.o)
    0x080001aa   0x080001aa   0x00000000   Code   RO         4160    .ARM.Collect$$libshutdown$$00000006  c_w.l(libshutdown2.o)
    0x080001aa   0x080001aa   0x00000000   Code   RO         4163    .ARM.Collect$$libshutdown$$00000009  c_w.l(libshutdown2.o)
    0x080001aa   0x080001aa   0x00000000   Code   RO         4166    .ARM.Collect$$libshutdown$$0000000C  c_w.l(libshutdown2.o)
    0x080001aa   0x080001aa   0x00000000   Code   RO         4168    .ARM.Collect$$libshutdown$$0000000E  c_w.l(libshutdown2.o)
    0x080001aa   0x080001aa   0x00000000   Code   RO         4171    .ARM.Collect$$libshutdown$$00000011  c_w.l(libshutdown2.o)
    0x080001aa   0x080001aa   0x00000002   Code   RO         4172    .ARM.Collect$$libshutdown$$00000012  c_w.l(libshutdown2.o)
    0x080001ac   0x080001ac   0x00000000   Code   RO         4034    .ARM.Collect$$rtentry$$00000000  c_w.l(__rtentry.o)
    0x080001ac   0x080001ac   0x00000000   Code   RO         4051    .ARM.Collect$$rtentry$$00000002  c_w.l(__rtentry2.o)
    0x080001ac   0x080001ac   0x00000006   Code   RO         4063    .ARM.Collect$$rtentry$$00000004  c_w.l(__rtentry4.o)
    0x080001b2   0x080001b2   0x00000000   Code   RO         4053    .ARM.Collect$$rtentry$$00000009  c_w.l(__rtentry2.o)
    0x080001b2   0x080001b2   0x00000004   Code   RO         4054    .ARM.Collect$$rtentry$$0000000A  c_w.l(__rtentry2.o)
    0x080001b6   0x080001b6   0x00000000   Code   RO         4056    .ARM.Collect$$rtentry$$0000000C  c_w.l(__rtentry2.o)
    0x080001b6   0x080001b6   0x00000008   Code   RO         4057    .ARM.Collect$$rtentry$$0000000D  c_w.l(__rtentry2.o)
    0x080001be   0x080001be   0x00000002   Code   RO         4082    .ARM.Collect$$rtexit$$00000000  c_w.l(rtexit.o)
    0x080001c0   0x080001c0   0x00000000   Code   RO         4130    .ARM.Collect$$rtexit$$00000002  c_w.l(rtexit2.o)
    0x080001c0   0x080001c0   0x00000004   Code   RO         4131    .ARM.Collect$$rtexit$$00000003  c_w.l(rtexit2.o)
    0x080001c4   0x080001c4   0x00000006   Code   RO         4132    .ARM.Collect$$rtexit$$00000004  c_w.l(rtexit2.o)
    0x080001ca   0x080001ca   0x00000002   PAD
    0x080001cc   0x080001cc   0x00000040   Code   RO          133    .text               startup_stm32f10x_hd.o
    0x0800020c   0x0800020c   0x00000002   Code   RO         3970    .text               c_w.l(use_no_semi_2.o)
    0x0800020e   0x0800020e   0x00000006   Code   RO         3972    .text               c_w.l(heapauxi.o)
    0x08000214   0x08000214   0x00000002   Code   RO         4032    .text               c_w.l(use_no_semi.o)
    0x08000216   0x08000216   0x0000004a   Code   RO         4067    .text               c_w.l(sys_stackheap_outer.o)
    0x08000260   0x08000260   0x00000012   Code   RO         4069    .text               c_w.l(exit.o)
    0x08000272   0x08000272   0x00000002   PAD
    0x08000274   0x08000274   0x00000008   Code   RO         4079    .text               c_w.l(libspace.o)
    0x0800027c   0x0800027c   0x00000084   Code   RO         3458    i.Encoder1_Init     encoder.o
    0x08000300   0x08000300   0x000000a0   Code   RO         3459    i.Encoder2_Init     encoder.o
    0x080003a0   0x080003a0   0x0000007c   Code   RO         3460    i.Encoder3_Init     encoder.o
    0x0800041c   0x0800041c   0x0000007c   Code   RO         3461    i.Encoder4_Init     encoder.o
    0x08000498   0x08000498   0x00000014   Code   RO         3462    i.Encoder_Init      encoder.o
    0x080004ac   0x080004ac   0x00000116   Code   RO         1350    i.GPIO_Init         stm32f10x_gpio.o
    0x080005c2   0x080005c2   0x00000002   PAD
    0x080005c4   0x080005c4   0x00000090   Code   RO         1352    i.GPIO_PinRemapConfig  stm32f10x_gpio.o
    0x08000654   0x08000654   0x00000004   Code   RO         1357    i.GPIO_ResetBits    stm32f10x_gpio.o
    0x08000658   0x08000658   0x00000004   Code   RO         1358    i.GPIO_SetBits      stm32f10x_gpio.o
    0x0800065c   0x0800065c   0x0000000a   Code   RO         1361    i.GPIO_WriteBit     stm32f10x_gpio.o
    0x08000666   0x08000666   0x00000002   PAD
    0x08000668   0x08000668   0x00000064   Code   RO         3512    i.Get_Accumulated_Position  pid_control.o
    0x080006cc   0x080006cc   0x00000050   Code   RO         3463    i.Get_Encoder_Count  encoder.o
    0x0800071c   0x0800071c   0x0000001c   Code   RO         3513    i.Get_Motor_Speed   pid_control.o
    0x08000738   0x08000738   0x0000010c   Code   RO         3229    i.Hardware_PWM_Init  tb6612.o
    0x08000844   0x08000844   0x00000050   Code   RO         3872    i.IIC_Ack           iic.o
    0x08000894   0x08000894   0x00000040   Code   RO         3873    i.IIC_Init          iic.o
    0x080008d4   0x080008d4   0x00000050   Code   RO         3874    i.IIC_NAck          iic.o
    0x08000924   0x08000924   0x00000070   Code   RO         3875    i.IIC_Read_Byte     iic.o
    0x08000994   0x08000994   0x00000068   Code   RO         3876    i.IIC_Send_Byte     iic.o
    0x080009fc   0x080009fc   0x0000004c   Code   RO         3877    i.IIC_Start         iic.o
    0x08000a48   0x08000a48   0x0000004c   Code   RO         3878    i.IIC_Stop          iic.o
    0x08000a94   0x08000a94   0x00000064   Code   RO         3879    i.IIC_Wait_Ack      iic.o
    0x08000af8   0x08000af8   0x0000002c   Code   RO         3514    i.Limit_Value       pid_control.o
    0x08000b24   0x08000b24   0x00000010   Code   RO         3230    i.MOTOR_Init        tb6612.o
    0x08000b34   0x08000b34   0x0000006c   Code   RO         3231    i.Motor_Direction_Init  tb6612.o
    0x08000ba0   0x08000ba0   0x00000124   Code   RO         3232    i.Motor_SetDirection  tb6612.o
    0x08000cc4   0x08000cc4   0x00000058   Code   RO         3233    i.Motor_SetPWM      tb6612.o
    0x08000d1c   0x08000d1c   0x00000044   Code   RO         3234    i.Move_stop         tb6612.o
    0x08000d60   0x08000d60   0x00000070   Code   RO          137    i.NVIC_Init         misc.o
    0x08000dd0   0x08000dd0   0x0000002a   Code   RO         3286    i.OLED_Clear        oled.o
    0x08000dfa   0x08000dfa   0x00000002   PAD
    0x08000dfc   0x08000dfc   0x0000004c   Code   RO         3287    i.OLED_I2C_Init     oled.o
    0x08000e48   0x08000e48   0x00000058   Code   RO         3288    i.OLED_I2C_SendByte  oled.o
    0x08000ea0   0x08000ea0   0x00000030   Code   RO         3289    i.OLED_I2C_Start    oled.o
    0x08000ed0   0x08000ed0   0x00000028   Code   RO         3290    i.OLED_I2C_Stop     oled.o
    0x08000ef8   0x08000ef8   0x000000ae   Code   RO         3291    i.OLED_Init         oled.o
    0x08000fa6   0x08000fa6   0x00000022   Code   RO         3293    i.OLED_SetCursor    oled.o
    0x08000fc8   0x08000fc8   0x00000020   Code   RO         3300    i.OLED_WriteCommand  oled.o
    0x08000fe8   0x08000fe8   0x00000020   Code   RO         3301    i.OLED_WriteData    oled.o
    0x08001008   0x08001008   0x0000001c   Code   RO         3394    i.PCA9685_Init      pca9685.o
    0x08001024   0x08001024   0x0000003a   Code   RO         3395    i.PCA9685_read      pca9685.o
    0x0800105e   0x0800105e   0x0000002e   Code   RO         3396    i.PCA9685_write     pca9685.o
    0x0800108c   0x0800108c   0x00000074   Code   RO         3517    i.PID_Control_Init  pid_control.o
    0x08001100   0x08001100   0x000000dc   Code   RO         3519    i.Position_PID_RPM  pid_control.o
    0x080011dc   0x080011dc   0x00000020   Code   RO         1778    i.RCC_APB1PeriphClockCmd  stm32f10x_rcc.o
    0x080011fc   0x080011fc   0x00000020   Code   RO         1780    i.RCC_APB2PeriphClockCmd  stm32f10x_rcc.o
    0x0800121c   0x0800121c   0x000000d4   Code   RO         1788    i.RCC_GetClocksFreq  stm32f10x_rcc.o
    0x080012f0   0x080012f0   0x00000050   Code   RO         3465    i.Reset_Encoder     encoder.o
    0x08001340   0x08001340   0x00000030   Code   RO         3521    i.Reset_Motor_State  pid_control.o
    0x08001370   0x08001370   0x00000008   Code   RO            1    i.SetSysClock       system_stm32f10x.o
    0x08001378   0x08001378   0x000000e0   Code   RO            2    i.SetSysClockTo72   system_stm32f10x.o
    0x08001458   0x08001458   0x00000060   Code   RO         3235    i.Set_Motor_PWM     tb6612.o
    0x080014b8   0x080014b8   0x0000003c   Code   RO         3522    i.Speed_Control_Start  pid_control.o
    0x080014f4   0x080014f4   0x00000104   Code   RO         3523    i.Speed_PID_Calculate  pid_control.o
    0x080015f8   0x080015f8   0x00000060   Code   RO            4    i.SystemInit        system_stm32f10x.o
    0x08001658   0x08001658   0x00000138   Code   RO         3524    i.TIM6_IRQHandler   pid_control.o
    0x08001790   0x08001790   0x00000006   Code   RO         2419    i.TIM_ClearITPendingBit  stm32f10x_tim.o
    0x08001796   0x08001796   0x00000018   Code   RO         2424    i.TIM_Cmd           stm32f10x_tim.o
    0x080017ae   0x080017ae   0x0000001e   Code   RO         2426    i.TIM_CtrlPWMOutputs  stm32f10x_tim.o
    0x080017cc   0x080017cc   0x00000042   Code   RO         2433    i.TIM_EncoderInterfaceConfig  stm32f10x_tim.o
    0x0800180e   0x0800180e   0x00000006   Code   RO         2443    i.TIM_GetCounter    stm32f10x_tim.o
    0x08001814   0x08001814   0x00000022   Code   RO         2445    i.TIM_GetITStatus   stm32f10x_tim.o
    0x08001836   0x08001836   0x00000012   Code   RO         2449    i.TIM_ITConfig      stm32f10x_tim.o
    0x08001848   0x08001848   0x00000098   Code   RO         2453    i.TIM_OC1Init       stm32f10x_tim.o
    0x080018e0   0x080018e0   0x00000012   Code   RO         2456    i.TIM_OC1PreloadConfig  stm32f10x_tim.o
    0x080018f2   0x080018f2   0x00000002   PAD
    0x080018f4   0x080018f4   0x000000a4   Code   RO         2458    i.TIM_OC2Init       stm32f10x_tim.o
    0x08001998   0x08001998   0x0000001a   Code   RO         2461    i.TIM_OC2PreloadConfig  stm32f10x_tim.o
    0x080019b2   0x080019b2   0x00000002   PAD
    0x080019b4   0x080019b4   0x000000a0   Code   RO         2463    i.TIM_OC3Init       stm32f10x_tim.o
    0x08001a54   0x08001a54   0x00000012   Code   RO         2466    i.TIM_OC3PreloadConfig  stm32f10x_tim.o
    0x08001a66   0x08001a66   0x00000002   PAD
    0x08001a68   0x08001a68   0x0000007c   Code   RO         2468    i.TIM_OC4Init       stm32f10x_tim.o
    0x08001ae4   0x08001ae4   0x0000001a   Code   RO         2470    i.TIM_OC4PreloadConfig  stm32f10x_tim.o
    0x08001afe   0x08001afe   0x00000004   Code   RO         2485    i.TIM_SetCompare1   stm32f10x_tim.o
    0x08001b02   0x08001b02   0x00000004   Code   RO         2486    i.TIM_SetCompare2   stm32f10x_tim.o
    0x08001b06   0x08001b06   0x00000004   Code   RO         2487    i.TIM_SetCompare3   stm32f10x_tim.o
    0x08001b0a   0x08001b0a   0x00000006   Code   RO         2488    i.TIM_SetCompare4   stm32f10x_tim.o
    0x08001b10   0x08001b10   0x00000004   Code   RO         2489    i.TIM_SetCounter    stm32f10x_tim.o
    0x08001b14   0x08001b14   0x000000a4   Code   RO         2495    i.TIM_TimeBaseInit  stm32f10x_tim.o
    0x08001bb8   0x08001bb8   0x00000018   Code   RO         2963    i.USART_Cmd         stm32f10x_usart.o
    0x08001bd0   0x08001bd0   0x000000d8   Code   RO         2970    i.USART_Init        stm32f10x_usart.o
    0x08001ca8   0x08001ca8   0x0000007c   Code   RO         3787    i.Usart2_Init       usart2.o
    0x08001d24   0x08001d24   0x00000080   Code   RO         3820    i.Usart5_Init       usart5.o
    0x08001da4   0x08001da4   0x00000004   Code   RO         3824    i._sys_exit         usart5.o
    0x08001da8   0x08001da8   0x00000030   Code   RO         3713    i.delay_init        delay.o
    0x08001dd8   0x08001dd8   0x00000018   Code   RO         3714    i.delay_ms          delay.o
    0x08001df0   0x08001df0   0x0000003c   Code   RO         3716    i.delay_us          delay.o
    0x08001e2c   0x08001e2c   0x000000dc   Code   RO         4029    i.floor             m_ws.l(floor.o)
    0x08001f08   0x08001f08   0x0000002e   Code   RO         3929    i.init              main.o
    0x08001f36   0x08001f36   0x00000002   PAD
    0x08001f38   0x08001f38   0x0000002c   Code   RO         3930    i.main              main.o
    0x08001f64   0x08001f64   0x00000098   Code   RO         3400    i.setPWMFreq        pca9685.o
    0x08001ffc   0x08001ffc   0x00000150   Code   RO         3978    x$fpl$dadd          fz_ws.l(daddsub_clz.o)
    0x0800214c   0x0800214c   0x00000018   Code   RO         4077    x$fpl$dcmpinf       fz_ws.l(dcmpi.o)
    0x08002164   0x08002164   0x000002b0   Code   RO         3985    x$fpl$ddiv          fz_ws.l(ddiv.o)
    0x08002414   0x08002414   0x0000005a   Code   RO         3988    x$fpl$dfixu         fz_ws.l(dfixu.o)
    0x0800246e   0x0800246e   0x0000002e   Code   RO         3993    x$fpl$dflt          fz_ws.l(dflt_clz.o)
    0x0800249c   0x0800249c   0x00000078   Code   RO         4065    x$fpl$dleqf         fz_ws.l(dleqf.o)
    0x08002514   0x08002514   0x00000154   Code   RO         3998    x$fpl$dmul          fz_ws.l(dmul.o)
    0x08002668   0x08002668   0x0000009c   Code   RO         4035    x$fpl$dnaninf       fz_ws.l(dnaninf.o)
    0x08002704   0x08002704   0x0000000c   Code   RO         4037    x$fpl$dretinf       fz_ws.l(dretinf.o)
    0x08002710   0x08002710   0x0000006c   Code   RO         4039    x$fpl$drleqf        fz_ws.l(drleqf.o)
    0x0800277c   0x0800277c   0x00000016   Code   RO         3979    x$fpl$drsb          fz_ws.l(daddsub_clz.o)
    0x08002792   0x08002792   0x00000002   PAD
    0x08002794   0x08002794   0x000001d4   Code   RO         3980    x$fpl$dsub          fz_ws.l(daddsub_clz.o)
    0x08002968   0x08002968   0x000000c4   Code   RO         4002    x$fpl$fadd          fz_ws.l(faddsub_clz.o)
    0x08002a2c   0x08002a2c   0x00000018   Code   RO         4041    x$fpl$fcmpinf       fz_ws.l(fcmpi.o)
    0x08002a44   0x08002a44   0x00000184   Code   RO         4009    x$fpl$fdiv          fz_ws.l(fdiv.o)
    0x08002bc8   0x08002bc8   0x00000068   Code   RO         4012    x$fpl$feqf          fz_ws.l(feqf.o)
    0x08002c30   0x08002c30   0x00000036   Code   RO         4014    x$fpl$ffix          fz_ws.l(ffix.o)
    0x08002c66   0x08002c66   0x00000002   PAD
    0x08002c68   0x08002c68   0x00000030   Code   RO         4019    x$fpl$fflt          fz_ws.l(fflt_clz.o)
    0x08002c98   0x08002c98   0x00000068   Code   RO         4043    x$fpl$fleqf         fz_ws.l(fleqf.o)
    0x08002d00   0x08002d00   0x00000102   Code   RO         4024    x$fpl$fmul          fz_ws.l(fmul.o)
    0x08002e02   0x08002e02   0x0000008c   Code   RO         4045    x$fpl$fnaninf       fz_ws.l(fnaninf.o)
    0x08002e8e   0x08002e8e   0x0000000a   Code   RO         4047    x$fpl$fretinf       fz_ws.l(fretinf.o)
    0x08002e98   0x08002e98   0x00000062   Code   RO         4026    x$fpl$frleqf        fz_ws.l(frleqf.o)
    0x08002efa   0x08002efa   0x00000014   Code   RO         4003    x$fpl$frsb          fz_ws.l(faddsub_clz.o)
    0x08002f0e   0x08002f0e   0x00000002   PAD
    0x08002f10   0x08002f10   0x000000ea   Code   RO         4004    x$fpl$fsub          fz_ws.l(faddsub_clz.o)
    0x08002ffa   0x08002ffa   0x00000000   Code   RO         4049    x$fpl$usenofp       fz_ws.l(usenofp.o)
    0x08002ffa   0x08002ffa   0x00000002   PAD
    0x08002ffc   0x08002ffc   0x00000020   Data   RO         4204    Region$$Table       anon$$obj.o


    Execution Region RW_IRAM1 (Exec base: 0x20000000, Load base: 0x0800301c, Size: 0x00000760, Max: 0x00010000, ABSOLUTE)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x20000000   0x0800301c   0x00000014   Data   RW         1808    .data               stm32f10x_rcc.o
    0x20000014   0x08003030   0x00000024   Data   RW         3526    .data               pid_control.o
    0x20000038   0x08003054   0x00000004   Data   RW         3718    .data               delay.o
    0x2000003c        -       0x000000c0   Zero   RW         3525    .bss                pid_control.o
    0x200000fc        -       0x00000060   Zero   RW         4080    .bss                c_w.l(libspace.o)
    0x2000015c   0x08003058   0x00000004   PAD
    0x20000160        -       0x00000200   Zero   RW          131    HEAP                startup_stm32f10x_hd.o
    0x20000360        -       0x00000400   Zero   RW          130    STACK               startup_stm32f10x_hd.o


==============================================================================

Image component sizes


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Object Name

         0          0          0          0          0         32   core_cm3.o
       132         12          0          4          0       1607   delay.o
       720         78          0          0          0       3731   encoder.o
       692        108          0          0          0       3978   iic.o
        90          4          0          0          0        854   main.o
       112         12          0          0          0     205648   misc.o
       566         20          0          0          0       4508   oled.o
       284         18          0          0          0       2195   pca9685.o
      1188        124          0         36        192       7700   pid_control.o
        64         26        304          0       1536        788   startup_stm32f10x_hd.o
         0          0          0          0          0       1652   stm32f10x_adc.o
       440          6          0          0          0      12514   stm32f10x_gpio.o
       276         32          0         20          0      12950   stm32f10x_rcc.o
      1058         92          0          0          0      32782   stm32f10x_tim.o
       240          6          0          0          0       8713   stm32f10x_usart.o
       328         28          0          0          0      49689   system_stm32f10x.o
       936         52          0          0          0       5064   tb6612.o
       124         10          0          0          0        630   usart2.o
       132         14          0          0          0       1066   usart5.o

    ----------------------------------------------------------------------
      7396        <USER>        <GROUP>         60       1728     356101   Object Totals
         0          0         32          0          0          0   (incl. Generated)
        14          0          0          0          0          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Member Name

         8          0          0          0          0         68   __main.o
         0          0          0          0          0          0   __rtentry.o
        12          0          0          0          0          0   __rtentry2.o
         6          0          0          0          0          0   __rtentry4.o
        52          8          0          0          0          0   __scatter.o
        26          0          0          0          0          0   __scatter_copy.o
        28          0          0          0          0          0   __scatter_zi.o
        18          0          0          0          0         80   exit.o
         6          0          0          0          0        152   heapauxi.o
         2          0          0          0          0          0   libinit.o
         2          0          0          0          0          0   libinit2.o
         2          0          0          0          0          0   libshutdown.o
         2          0          0          0          0          0   libshutdown2.o
         8          4          0          0         96         68   libspace.o
         2          0          0          0          0          0   rtexit.o
        10          0          0          0          0          0   rtexit2.o
        74          0          0          0          0         80   sys_stackheap_outer.o
         2          0          0          0          0         68   use_no_semi.o
         2          0          0          0          0         68   use_no_semi_2.o
       826         16          0          0          0        348   daddsub_clz.o
        24          0          0          0          0         68   dcmpi.o
       688        140          0          0          0        208   ddiv.o
        90          4          0          0          0         92   dfixu.o
        46          0          0          0          0         68   dflt_clz.o
       120          4          0          0          0         92   dleqf.o
       340         12          0          0          0        104   dmul.o
       156          4          0          0          0         92   dnaninf.o
        12          0          0          0          0         68   dretinf.o
       108          0          0          0          0         80   drleqf.o
       450          8          0          0          0        236   faddsub_clz.o
        24          0          0          0          0         68   fcmpi.o
       388         76          0          0          0         96   fdiv.o
       104          4          0          0          0         84   feqf.o
        54          4          0          0          0         84   ffix.o
        48          0          0          0          0         68   fflt_clz.o
       104          4          0          0          0         84   fleqf.o
       258          4          0          0          0         84   fmul.o
       140          4          0          0          0         84   fnaninf.o
        10          0          0          0          0         68   fretinf.o
        98          0          0          0          0         68   frleqf.o
         0          0          0          0          0          0   usenofp.o
       220         16          0          0          0         84   floor.o

    ----------------------------------------------------------------------
      4584        <USER>          <GROUP>          0        100       2912   Library Totals
        14          0          0          0          4          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Name

       262         12          0          0         96        584   c_w.l
      4088        284          0          0          0       2244   fz_ws.l
       220         16          0          0          0         84   m_ws.l

    ----------------------------------------------------------------------
      4584        <USER>          <GROUP>          0        100       2912   Library Totals

    ----------------------------------------------------------------------

==============================================================================


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   

     11980        954        336         60       1828     353185   Grand Totals
     11980        954        336         60       1828     353185   ELF Image Totals
     11980        954        336         60          0          0   ROM Totals

==============================================================================

    Total RO  Size (Code + RO Data)                12316 (  12.03kB)
    Total RW  Size (RW Data + ZI Data)              1888 (   1.84kB)
    Total ROM Size (Code + RO Data + RW Data)      12376 (  12.09kB)

==============================================================================

